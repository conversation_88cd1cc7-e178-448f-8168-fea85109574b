<template>
  <div class="colors-container">
    <!-- 顶部导航 -->
    <header class="header">
      <div class="nav-container">
        <button class="back-button" @click="goBack">
          <span class="emoji-icon">←</span>
        </button>
        <div class="page-info">
          <h1>色彩库</h1>
          <p>专业色彩资源，助您灵感获取</p>
        </div>
      </div>
    </header>

    <!-- 主要内容 -->
    <main class="colors-content">
      <div class="colors-container-inner">
        <!-- 功能导航卡片 -->
        <div class="feature-cards">
          <div 
            v-for="feature in colorFeatures" 
            :key="feature.key"
            class="feature-card"
            @click="navigateToFeature(feature)"
          >
            <div class="feature-icon">
              <span class="emoji-icon">{{ feature.icon }}</span>
            </div>
            <div class="feature-info">
              <h3>{{ feature.title }}</h3>
              <p>{{ feature.description }}</p>
            </div>
            <div class="feature-arrow">
              <span class="emoji-icon">→</span>
            </div>
          </div>
        </div>

        <!-- 流行色板预览 -->
        <div class="popular-section">
          <div class="section-header">
            <h2>
              <span class="emoji-icon">🔥</span>
              流行色板
            </h2>
            <button class="view-all-button" @click="viewAllPopular">
              查看全部
              <span class="emoji-icon">→</span>
            </button>
          </div>
          
          <div class="popular-grid">
            <div 
              v-for="palette in popularPalettes.slice(0, 6)" 
              :key="palette.id"
              class="popular-item"
              @click="viewPalette(palette)"
            >
              <div class="palette-colors">
                <div 
                  v-for="color in palette.colors" 
                  :key="color"
                  class="palette-color"
                  :style="{ backgroundColor: color }"
                ></div>
              </div>
              <div class="palette-info">
                <h4>{{ palette.title }}</h4>
                <div class="palette-meta">
                  <span class="palette-likes">
                    <span class="emoji-icon">❤️</span>
                    {{ palette.likes }}
                  </span>
                  <span class="palette-views">
                    <span class="emoji-icon">👁️</span>
                    {{ palette.views }}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 标准色库预览 -->
        <div class="standard-section">
          <div class="section-header">
            <h2>
              <span class="emoji-icon">📚</span>
              标准色库
            </h2>
            <button class="view-all-button" @click="viewAllStandard">
              查看全部
              <span class="emoji-icon">→</span>
            </button>
          </div>
          
          <div class="standard-grid">
            <div 
              v-for="brand in standardBrands.slice(0, 8)" 
              :key="brand.id"
              class="brand-item"
              @click="viewBrand(brand)"
            >
              <div class="brand-logo">
                <span class="emoji-icon">{{ brand.icon }}</span>
              </div>
              <div class="brand-info">
                <h4>{{ brand.name }}</h4>
                <p>{{ brand.colorCount }} 种颜色</p>
              </div>
              <div class="brand-preview">
                <div 
                  v-for="color in brand.previewColors" 
                  :key="color"
                  class="brand-color"
                  :style="{ backgroundColor: color }"
                ></div>
              </div>
            </div>
          </div>
        </div>

        <!-- 色彩工具 -->
        <div class="tools-section">
          <div class="section-header">
            <h2>
              <span class="emoji-icon">🛠️</span>
              色彩工具箱
            </h2>
          </div>
          
          <div class="tools-grid">
            <div 
              v-for="tool in colorTools" 
              :key="tool.key"
              class="tool-item"
              @click="navigateToTool(tool)"
            >
              <div class="tool-icon">
                <span class="emoji-icon">{{ tool.icon }}</span>
              </div>
              <div class="tool-info">
                <h4>{{ tool.title }}</h4>
                <p>{{ tool.description }}</p>
              </div>
            </div>
          </div>
        </div>

        <!-- 最近浏览 -->
        <div class="recent-section" v-if="recentColors.length > 0">
          <div class="section-header">
            <h2>
              <span class="emoji-icon">🕒</span>
              最近浏览
            </h2>
            <button class="clear-button" @click="clearRecent">
              清空
            </button>
          </div>
          
          <div class="recent-grid">
            <div 
              v-for="color in recentColors" 
              :key="color.id"
              class="recent-item"
              @click="viewColor(color)"
            >
              <div class="recent-color" :style="{ backgroundColor: color.hex }"></div>
              <div class="recent-info">
                <div class="recent-name">{{ color.name }}</div>
                <div class="recent-code">{{ color.code || color.hex }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </main>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'

const router = useRouter()

// 功能导航
const colorFeatures = ref([
  {
    key: 'popular',
    title: '流行色库',
    description: '大量流行色板，支持在线应用、导出以及与人分享',
    icon: '🔥',
    route: '/colors/popular'
  },
  {
    key: 'standard',
    title: '标准色库',
    description: '权威标准色彩体系，专业色彩参考',
    icon: '📚',
    route: '/colors/standard'
  },
  {
    key: 'palette',
    title: '配色空间',
    description: '智能配色建议，色彩搭配灵感',
    icon: '🎨',
    route: '/palette'
  },
  {
    key: 'trends',
    title: '色彩趋势',
    description: '最新色彩流行趋势，把握时尚脉搏',
    icon: '📈',
    route: '/colors/trends'
  }
])

// 流行色板数据
const popularPalettes = ref([
  {
    id: 1,
    title: '现代极简主义',
    colors: ['#2C3E50', '#34495E', '#7F8C8D', '#BDC3C7', '#ECF0F1'],
    likes: 1234,
    views: 5678,
    category: 'modern'
  },
  {
    id: 2,
    title: '温暖秋日',
    colors: ['#D35400', '#E67E22', '#F39C12', '#F1C40F', '#F4D03F'],
    likes: 987,
    views: 3456,
    category: 'nature'
  },
  {
    id: 3,
    title: '海洋深蓝',
    colors: ['#1B4F72', '#2E86AB', '#A23B72', '#F18F01', '#C73E1D'],
    likes: 756,
    views: 2345,
    category: 'nature'
  },
  {
    id: 4,
    title: '粉色梦境',
    colors: ['#FCE4EC', '#F8BBD9', '#F48FB1', '#F06292', '#E91E63'],
    likes: 1456,
    views: 6789,
    category: 'romantic'
  },
  {
    id: 5,
    title: '森林绿意',
    colors: ['#1B5E20', '#2E7D32', '#388E3C', '#4CAF50', '#66BB6A'],
    likes: 634,
    views: 1987,
    category: 'nature'
  },
  {
    id: 6,
    title: '复古怀旧',
    colors: ['#8D6E63', '#A1887F', '#BCAAA4', '#D7CCC8', '#EFEBE9'],
    likes: 892,
    views: 4321,
    category: 'vintage'
  }
])

// 标准色库品牌
const standardBrands = ref([
  {
    id: 1,
    name: 'Pantone',
    icon: '🎨',
    colorCount: 2156,
    previewColors: ['#FF6B35', '#F7931E', '#FFD23F', '#06FFA5', '#4ECDC4']
  },
  {
    id: 2,
    name: 'RAL',
    icon: '🏭',
    colorCount: 1625,
    previewColors: ['#C40233', '#FF7900', '#FDDA0D', '#6BA6CD', '#1F3A93']
  },
  {
    id: 3,
    name: 'NCS',
    icon: '🌈',
    colorCount: 1950,
    previewColors: ['#009639', '#0087BD', '#702F8A', '#E30613', '#F39800']
  },
  {
    id: 4,
    name: 'Munsell',
    icon: '🔬',
    colorCount: 1269,
    previewColors: ['#7B68EE', '#20B2AA', '#FFB347', '#FF69B4', '#32CD32']
  },
  {
    id: 5,
    name: 'CMYK',
    icon: '🖨️',
    colorCount: 2048,
    previewColors: ['#00FFFF', '#FF00FF', '#FFFF00', '#000000', '#FFFFFF']
  },
  {
    id: 6,
    name: 'RGB',
    icon: '💻',
    colorCount: 16777216,
    previewColors: ['#FF0000', '#00FF00', '#0000FF', '#FFFF00', '#FF00FF']
  },
  {
    id: 7,
    name: 'HSB',
    icon: '🎯',
    colorCount: 3600,
    previewColors: ['#FF4500', '#32CD32', '#1E90FF', '#FFD700', '#DA70D6']
  },
  {
    id: 8,
    name: 'LAB',
    icon: '🧪',
    colorCount: 1677721,
    previewColors: ['#8B4513', '#2F4F4F', '#FF1493', '#00CED1', '#9ACD32']
  }
])

// 色彩工具
const colorTools = ref([
  {
    key: 'convert',
    title: '颜色转换',
    description: 'RGB、CMYK、HEX等格式互转',
    icon: '🔄',
    route: '/tools/convert'
  },
  {
    key: 'picker',
    title: '取色器',
    description: '屏幕取色，精确获取颜色值',
    icon: '🎯',
    route: '/tools/picker'
  },
  {
    key: 'contrast',
    title: '对比度检测',
    description: '检测颜色对比度，确保可访问性',
    icon: '⚖️',
    route: '/tools/contrast'
  },
  {
    key: 'harmony',
    title: '色彩和谐',
    description: '生成和谐的色彩搭配方案',
    icon: '🎵',
    route: '/tools/harmony'
  }
])

// 最近浏览
const recentColors = ref([
  {
    id: 1,
    name: '中国红',
    hex: '#DC143C',
    code: 'C0M100Y100K0'
  },
  {
    id: 2,
    name: '天空蓝',
    hex: '#87CEEB',
    code: 'C100M0Y0K0'
  },
  {
    id: 3,
    name: '翡翠绿',
    hex: '#50C878',
    code: 'C80M0Y100K0'
  }
])

// 方法
const navigateToFeature = (feature: any) => {
  router.push(feature.route)
}

const viewAllPopular = () => {
  router.push('/colors/popular')
}

const viewAllStandard = () => {
  router.push('/colors/standard')
}

const viewPalette = (palette: any) => {
  router.push(`/palette/${palette.id}`)
}

const viewBrand = (brand: any) => {
  router.push(`/colors/brand/${brand.id}`)
}

const navigateToTool = (tool: any) => {
  router.push(tool.route)
}

const viewColor = (color: any) => {
  router.push(`/color/${color.hex.replace('#', '')}`)
}

const clearRecent = () => {
  recentColors.value = []
}

const goBack = () => {
  router.back()
}

onMounted(() => {
  // 初始化数据
})
</script>

<style scoped>
.colors-container {
  min-height: 100vh;
  background: #f5f5f5;
}

.header {
  background: white;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  padding: 16px 20px;
  position: sticky;
  top: 0;
  z-index: 100;
}

.nav-container {
  display: flex;
  align-items: center;
  gap: 16px;
  max-width: 1200px;
  margin: 0 auto;
}

.back-button {
  width: 40px;
  height: 40px;
  border: none;
  background: #f0f0f0;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.back-button:hover {
  background: #e0e0e0;
}

.back-button .emoji-icon {
  font-size: 20px;
  color: #666;
}

.page-info h1 {
  margin: 0;
  font-size: 20px;
  color: #333;
}

.page-info p {
  margin: 4px 0 0 0;
  font-size: 14px;
  color: #666;
}

.colors-content {
  padding: 20px;
}

.colors-container-inner {
  max-width: 1200px;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  gap: 32px;
}

/* 功能卡片 */
.feature-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 16px;
}

.feature-card {
  background: white;
  border-radius: 12px;
  padding: 20px;
  display: flex;
  align-items: center;
  gap: 16px;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.feature-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.feature-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background: linear-gradient(135deg, #d32f2f, #f44336);
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.feature-icon .emoji-icon {
  font-size: 24px;
}

.feature-info {
  flex: 1;
}

.feature-info h3 {
  margin: 0 0 4px 0;
  font-size: 16px;
  color: #333;
}

.feature-info p {
  margin: 0;
  font-size: 14px;
  color: #666;
  line-height: 1.4;
}

.feature-arrow {
  color: #d32f2f;
  font-size: 18px;
}

/* 区块标题 */
.section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20px;
}

.section-header h2 {
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 0;
  font-size: 20px;
  color: #333;
}

.section-header h2 .emoji-icon {
  font-size: 22px;
}

.view-all-button, .clear-button {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 16px;
  border: 1px solid #d32f2f;
  background: white;
  color: #d32f2f;
  border-radius: 20px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.3s ease;
}

.view-all-button:hover, .clear-button:hover {
  background: #d32f2f;
  color: white;
}

/* 流行色板 */
.popular-section {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.popular-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 16px;
}

.popular-item {
  border-radius: 12px;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 1px solid #e0e0e0;
}

.popular-item:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.palette-colors {
  display: flex;
  height: 80px;
}

.palette-color {
  flex: 1;
}

.palette-info {
  padding: 12px;
}

.palette-info h4 {
  margin: 0 0 8px 0;
  font-size: 14px;
  color: #333;
}

.palette-meta {
  display: flex;
  gap: 16px;
  font-size: 12px;
  color: #666;
}

.palette-likes, .palette-views {
  display: flex;
  align-items: center;
  gap: 4px;
}

/* 标准色库 */
.standard-section {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.standard-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 16px;
}

.brand-item {
  padding: 16px;
  border: 1px solid #e0e0e0;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.brand-item:hover {
  border-color: #d32f2f;
  transform: translateY(-2px);
}

.brand-logo {
  text-align: center;
  margin-bottom: 12px;
}

.brand-logo .emoji-icon {
  font-size: 32px;
}

.brand-info {
  text-align: center;
  margin-bottom: 12px;
}

.brand-info h4 {
  margin: 0 0 4px 0;
  font-size: 14px;
  color: #333;
}

.brand-info p {
  margin: 0;
  font-size: 12px;
  color: #666;
}

.brand-preview {
  display: flex;
  height: 20px;
  border-radius: 4px;
  overflow: hidden;
}

.brand-color {
  flex: 1;
}

/* 工具区域 */
.tools-section {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.tools-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 16px;
}

.tool-item {
  padding: 16px;
  border: 1px solid #e0e0e0;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: center;
}

.tool-item:hover {
  border-color: #d32f2f;
  transform: translateY(-2px);
}

.tool-icon {
  margin-bottom: 12px;
}

.tool-icon .emoji-icon {
  font-size: 32px;
}

.tool-info h4 {
  margin: 0 0 4px 0;
  font-size: 14px;
  color: #333;
}

.tool-info p {
  margin: 0;
  font-size: 12px;
  color: #666;
  line-height: 1.3;
}

/* 最近浏览 */
.recent-section {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.recent-grid {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

.recent-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.recent-item:hover {
  border-color: #d32f2f;
}

.recent-color {
  width: 24px;
  height: 24px;
  border-radius: 4px;
  border: 1px solid #e0e0e0;
}

.recent-info {
  font-size: 12px;
}

.recent-name {
  color: #333;
  font-weight: 500;
}

.recent-code {
  color: #666;
  font-family: 'Courier New', monospace;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .colors-content {
    padding: 16px;
  }
  
  .feature-cards {
    grid-template-columns: 1fr;
  }
  
  .popular-grid, .standard-grid, .tools-grid {
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
  }
  
  .section-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }
}
</style>

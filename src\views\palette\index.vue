<template>
  <div class="palette-container">
    <!-- 顶部导航 -->
    <header class="header">
      <div class="nav-container">
        <button class="back-button" @click="goBack">
          <span class="emoji-icon">←</span>
        </button>
        <div class="page-info">
          <h1>流行色板</h1>
          <p>发现最新的色彩搭配趋势</p>
        </div>
      </div>
    </header>

    <!-- 主要内容 -->
    <main class="palette-content">
      <div class="palette-container-inner">
        <!-- 分类标签 -->
        <div class="category-tabs">
          <button 
            v-for="category in categories" 
            :key="category.key"
            :class="['category-tab', { active: activeCategory === category.key }]"
            @click="activeCategory = category.key"
          >
            <span class="emoji-icon">{{ category.icon }}</span>
            {{ category.label }}
          </button>
        </div>

        <!-- 色板列表 -->
        <div class="palettes-grid">
          <div 
            v-for="palette in filteredPalettes" 
            :key="palette.id"
            class="palette-card"
            @click="viewPalette(palette)"
          >
            <div class="palette-preview">
              <div class="palette-colors">
                <div 
                  v-for="color in palette.colors" 
                  :key="color"
                  class="palette-color"
                  :style="{ backgroundColor: color }"
                ></div>
              </div>
            </div>
            <div class="palette-info">
              <h3 class="palette-title">{{ palette.title }}</h3>
              <p class="palette-description">{{ palette.description }}</p>
              <div class="palette-meta">
                <span class="palette-category">{{ getCategoryLabel(palette.category) }}</span>
                <span class="palette-colors-count">{{ palette.colors.length }} 色</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 加载更多 -->
        <div class="load-more" v-if="hasMore">
          <button class="load-more-button" @click="loadMore" :disabled="loading">
            <span class="emoji-icon">{{ loading ? '⏳' : '📥' }}</span>
            {{ loading ? '加载中...' : '加载更多' }}
          </button>
        </div>
      </div>
    </main>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'

interface Palette {
  id: number
  title: string
  description: string
  category: string
  colors: string[]
}

const router = useRouter()

const activeCategory = ref('all')
const loading = ref(false)
const hasMore = ref(true)

const categories = [
  { key: 'all', label: '全部', icon: '🎨' },
  { key: 'modern', label: '现代简约', icon: '🏢' },
  { key: 'nature', label: '自然田园', icon: '🌿' },
  { key: 'vintage', label: '复古怀旧', icon: '📻' },
  { key: 'romantic', label: '浪漫温馨', icon: '💕' },
  { key: 'professional', label: '商务专业', icon: '💼' },
  { key: 'creative', label: '创意艺术', icon: '🎭' }
]

const palettes = ref<Palette[]>([
  {
    id: 1,
    title: '摩登极简风格',
    description: '现代都市生活的完美诠释，简约而不简单',
    category: 'modern',
    colors: ['#823E3F', '#F9F0E1', '#866753', '#EFD658', '#6B9297']
  },
  {
    id: 2,
    title: '清新田园风色彩',
    description: '自然清新的田园风格，带来宁静舒适的感受',
    category: 'nature',
    colors: ['#14A699', '#FFD608', '#FFF6ED', '#ABF0D1', '#D0D0D0']
  },
  {
    id: 3,
    title: '浪漫地中海色彩',
    description: '地中海风情的浪漫色彩，蓝天白云的美好',
    category: 'romantic',
    colors: ['#6EA4D2', '#8B9DC5', '#9BCCDD', '#BCC6DF', '#E9E7F5']
  },
  {
    id: 4,
    title: '优雅复古墙面漆',
    description: '复古优雅的色彩搭配，展现时光沉淀的美',
    category: 'vintage',
    colors: ['#FCE0C8', '#7A4438', '#F0D8BE', '#CCB89F', '#D4C0A7']
  },
  {
    id: 5,
    title: '马卡龙甜美色彩',
    description: '甜美可爱的马卡龙色系，少女心满满',
    category: 'romantic',
    colors: ['#D7D2B4', '#D5B87C', '#FB6571', '#EDCFB3', '#F4B0A5']
  },
  {
    id: 6,
    title: '莫兰迪色系',
    description: '高级灰调的莫兰迪色系，低饱和度的优雅',
    category: 'modern',
    colors: ['#EBD9CB', '#F9F6F1', '#DBE2EC', '#8D91AA', '#E2E2E2']
  },
  {
    id: 7,
    title: '商务专业配色',
    description: '适合商务场合的专业配色方案',
    category: 'professional',
    colors: ['#2C3E50', '#34495E', '#7F8C8D', '#BDC3C7', '#ECF0F1']
  },
  {
    id: 8,
    title: '创意艺术色彩',
    description: '充满创意和艺术感的色彩组合',
    category: 'creative',
    colors: ['#E74C3C', '#F39C12', '#F1C40F', '#2ECC71', '#3498DB']
  }
])

const filteredPalettes = computed(() => {
  if (activeCategory.value === 'all') {
    return palettes.value
  }
  return palettes.value.filter(palette => palette.category === activeCategory.value)
})

const getCategoryLabel = (categoryKey: string) => {
  const category = categories.find(cat => cat.key === categoryKey)
  return category ? category.label : categoryKey
}

const viewPalette = (palette: Palette) => {
  // 跳转到色板详情页面或展开详情
  console.log('查看色板:', palette)
  // 这里可以实现色板详情的展示逻辑
}

const loadMore = () => {
  loading.value = true
  // 模拟加载更多数据
  setTimeout(() => {
    // 这里可以添加更多色板数据
    loading.value = false
    hasMore.value = false // 假设没有更多数据了
  }, 1000)
}

const goBack = () => {
  router.back()
}

onMounted(() => {
  // 初始化数据
})
</script>

<style scoped>
.palette-container {
  min-height: 100vh;
  background: #f5f5f5;
}

.header {
  background: white;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  padding: 16px 20px;
  position: sticky;
  top: 0;
  z-index: 100;
}

.nav-container {
  display: flex;
  align-items: center;
  gap: 16px;
  max-width: 1200px;
  margin: 0 auto;
}

.back-button {
  width: 40px;
  height: 40px;
  border: none;
  background: #f0f0f0;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.back-button:hover {
  background: #e0e0e0;
}

.back-button .emoji-icon {
  font-size: 20px;
  color: #666;
}

.page-info h1 {
  margin: 0;
  font-size: 20px;
  color: #333;
}

.page-info p {
  margin: 4px 0 0 0;
  font-size: 14px;
  color: #666;
}

.palette-content {
  padding: 20px;
}

.palette-container-inner {
  max-width: 1200px;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.category-tabs {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
  background: white;
  padding: 16px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.category-tab {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 16px;
  border: none;
  background: #f5f5f5;
  color: #666;
  border-radius: 20px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 14px;
}

.category-tab.active {
  background: #d32f2f;
  color: white;
}

.category-tab:hover {
  background: #e0e0e0;
}

.category-tab.active:hover {
  background: #b71c1c;
}

.category-tab .emoji-icon {
  font-size: 16px;
}

.palettes-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;
}

.palette-card {
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  cursor: pointer;
  transition: all 0.3s ease;
}

.palette-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.palette-preview {
  height: 120px;
  overflow: hidden;
}

.palette-colors {
  display: flex;
  height: 100%;
}

.palette-color {
  flex: 1;
  transition: all 0.3s ease;
}

.palette-card:hover .palette-color {
  transform: scaleY(1.1);
}

.palette-info {
  padding: 16px;
}

.palette-title {
  margin: 0 0 8px 0;
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.palette-description {
  margin: 0 0 12px 0;
  font-size: 14px;
  color: #666;
  line-height: 1.4;
}

.palette-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 12px;
  color: #999;
}

.palette-category {
  background: #f0f0f0;
  padding: 4px 8px;
  border-radius: 12px;
}

.palette-colors-count {
  font-weight: 500;
}

.load-more {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

.load-more-button {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 24px;
  border: none;
  background: white;
  color: #666;
  border-radius: 8px;
  cursor: pointer;
  font-size: 14px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  transition: all 0.3s ease;
}

.load-more-button:hover:not(:disabled) {
  background: #f5f5f5;
  transform: translateY(-2px);
}

.load-more-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.load-more-button .emoji-icon {
  font-size: 16px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .palette-content {
    padding: 16px;
  }
  
  .palettes-grid {
    grid-template-columns: 1fr;
  }
  
  .category-tabs {
    padding: 12px;
  }
  
  .category-tab {
    padding: 6px 12px;
    font-size: 12px;
  }
}
</style>

<template>
  <div class="standard-container">
    <!-- 顶部导航 -->
    <header class="header">
      <div class="nav-container">
        <button class="back-button" @click="goBack">
          <span class="emoji-icon">←</span>
        </button>
        <div class="page-info">
          <h1>标准色库</h1>
          <p>权威标准色彩体系，专业色彩参考</p>
        </div>
      </div>
    </header>

    <!-- 品牌筛选 -->
    <div class="filter-section">
      <div class="filter-container">
        <div class="brand-filters">
          <button 
            v-for="brand in colorBrands" 
            :key="brand.key"
            :class="['brand-btn', { active: activeBrand === brand.key }]"
            @click="activeBrand = brand.key"
          >
            <span class="emoji-icon">{{ brand.icon }}</span>
            {{ brand.name }}
            <span class="color-count">({{ brand.colorCount }})</span>
          </button>
        </div>
      </div>
    </div>

    <!-- 色彩网格 -->
    <main class="standard-content">
      <div class="standard-container-inner">
        <!-- 品牌介绍 -->
        <div class="brand-intro" v-if="currentBrand">
          <div class="brand-header">
            <div class="brand-logo">
              <span class="emoji-icon">{{ currentBrand.icon }}</span>
            </div>
            <div class="brand-details">
              <h2>{{ currentBrand.name }}</h2>
              <p>{{ currentBrand.description }}</p>
              <div class="brand-stats">
                <span class="stat">
                  <span class="emoji-icon">🎨</span>
                  {{ currentBrand.colorCount }} 种颜色
                </span>
                <span class="stat">
                  <span class="emoji-icon">📅</span>
                  成立于 {{ currentBrand.foundedYear }}
                </span>
                <span class="stat">
                  <span class="emoji-icon">🌍</span>
                  {{ currentBrand.coverage }}
                </span>
              </div>
            </div>
          </div>
        </div>

        <!-- 搜索和筛选 -->
        <div class="search-section">
          <div class="search-controls">
            <div class="search-input-group">
              <input 
                v-model="searchQuery"
                type="text" 
                placeholder="搜索颜色名称或色号..."
                class="search-input"
              />
              <button class="search-btn">
                <span class="emoji-icon">🔍</span>
              </button>
            </div>
            
            <div class="filter-controls">
              <select v-model="colorCategory" class="filter-select">
                <option value="all">所有分类</option>
                <option value="red">红色系</option>
                <option value="blue">蓝色系</option>
                <option value="green">绿色系</option>
                <option value="yellow">黄色系</option>
                <option value="purple">紫色系</option>
                <option value="orange">橙色系</option>
                <option value="neutral">中性色</option>
              </select>
              
              <select v-model="sortOrder" class="filter-select">
                <option value="name">按名称排序</option>
                <option value="code">按色号排序</option>
                <option value="hue">按色相排序</option>
                <option value="lightness">按明度排序</option>
              </select>
            </div>
          </div>
        </div>

        <!-- 颜色网格 -->
        <div class="colors-grid">
          <div 
            v-for="color in filteredColors" 
            :key="color.id"
            class="color-item"
            @click="viewColor(color)"
          >
            <div class="color-swatch" :style="{ backgroundColor: color.hex }"></div>
            <div class="color-info">
              <h4 class="color-name">{{ color.name }}</h4>
              <p class="color-code">{{ color.code }}</p>
              <p class="color-hex">{{ color.hex }}</p>
              <div class="color-values">
                <span class="color-rgb">RGB({{ color.rgb.r }}, {{ color.rgb.g }}, {{ color.rgb.b }})</span>
              </div>
            </div>
            <div class="color-actions">
              <button class="action-btn" @click.stop="copyColor(color.hex)">
                <span class="emoji-icon">📋</span>
              </button>
              <button class="action-btn" @click.stop="likeColor(color)">
                <span class="emoji-icon">{{ color.isLiked ? '❤️' : '🤍' }}</span>
              </button>
            </div>
          </div>
        </div>

        <!-- 分页 -->
        <div class="pagination" v-if="totalPages > 1">
          <button 
            class="page-btn"
            :disabled="currentPage === 1"
            @click="currentPage--"
          >
            <span class="emoji-icon">←</span>
            上一页
          </button>
          
          <div class="page-numbers">
            <button 
              v-for="page in visiblePages" 
              :key="page"
              :class="['page-number', { active: page === currentPage }]"
              @click="currentPage = page"
            >
              {{ page }}
            </button>
          </div>
          
          <button 
            class="page-btn"
            :disabled="currentPage === totalPages"
            @click="currentPage++"
          >
            下一页
            <span class="emoji-icon">→</span>
          </button>
        </div>
      </div>
    </main>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { useRouter } from 'vue-router'

interface Color {
  id: number
  name: string
  code: string
  hex: string
  rgb: { r: number; g: number; b: number }
  category: string
  brand: string
  isLiked: boolean
}

interface Brand {
  key: string
  name: string
  icon: string
  colorCount: number
  description: string
  foundedYear: number
  coverage: string
}

const router = useRouter()

const activeBrand = ref('pantone')
const searchQuery = ref('')
const colorCategory = ref('all')
const sortOrder = ref('name')
const currentPage = ref(1)
const pageSize = 24

const colorBrands: Brand[] = [
  {
    key: 'pantone',
    name: 'Pantone',
    icon: '🎨',
    colorCount: 2156,
    description: 'Pantone是全球色彩权威，为设计师和品牌提供专业色彩解决方案',
    foundedYear: 1963,
    coverage: '全球通用'
  },
  {
    key: 'ral',
    name: 'RAL',
    icon: '🏭',
    colorCount: 1625,
    description: 'RAL是欧洲标准色彩体系，广泛应用于建筑、工业和设计领域',
    foundedYear: 1927,
    coverage: '欧洲标准'
  },
  {
    key: 'ncs',
    name: 'NCS',
    icon: '🌈',
    colorCount: 1950,
    description: 'NCS自然色彩系统，基于人眼对色彩的感知建立的科学色彩体系',
    foundedYear: 1979,
    coverage: '北欧标准'
  },
  {
    key: 'munsell',
    name: 'Munsell',
    icon: '🔬',
    colorCount: 1269,
    description: 'Munsell色彩系统是最早的科学色彩分类系统之一',
    foundedYear: 1905,
    coverage: '科学标准'
  }
]

// 模拟颜色数据
const allColors = ref<Color[]>([
  {
    id: 1,
    name: 'Pantone Red 032',
    code: 'PANTONE 032 C',
    hex: '#EF3340',
    rgb: { r: 239, g: 51, b: 64 },
    category: 'red',
    brand: 'pantone',
    isLiked: false
  },
  {
    id: 2,
    name: 'Pantone Blue 286',
    code: 'PANTONE 286 C',
    hex: '#0033A0',
    rgb: { r: 0, g: 51, b: 160 },
    category: 'blue',
    brand: 'pantone',
    isLiked: true
  },
  {
    id: 3,
    name: 'RAL 3020',
    code: 'RAL 3020',
    hex: '#CC0605',
    rgb: { r: 204, g: 6, b: 5 },
    category: 'red',
    brand: 'ral',
    isLiked: false
  },
  {
    id: 4,
    name: 'NCS S 1080-Y90R',
    code: 'NCS S 1080-Y90R',
    hex: '#FF6B35',
    rgb: { r: 255, g: 107, b: 53 },
    category: 'orange',
    brand: 'ncs',
    isLiked: false
  },
  // 添加更多颜色数据...
])

const currentBrand = computed(() => {
  return colorBrands.find(brand => brand.key === activeBrand.value)
})

const filteredColors = computed(() => {
  let filtered = allColors.value.filter(color => color.brand === activeBrand.value)

  // 按分类筛选
  if (colorCategory.value !== 'all') {
    filtered = filtered.filter(color => color.category === colorCategory.value)
  }

  // 按搜索词筛选
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    filtered = filtered.filter(color => 
      color.name.toLowerCase().includes(query) ||
      color.code.toLowerCase().includes(query) ||
      color.hex.toLowerCase().includes(query)
    )
  }

  // 排序
  switch (sortOrder.value) {
    case 'code':
      filtered.sort((a, b) => a.code.localeCompare(b.code))
      break
    case 'hue':
      // 简单的色相排序（实际应该基于HSL的H值）
      filtered.sort((a, b) => a.hex.localeCompare(b.hex))
      break
    case 'lightness':
      // 简单的明度排序（实际应该基于HSL的L值）
      filtered.sort((a, b) => {
        const aLightness = (a.rgb.r + a.rgb.g + a.rgb.b) / 3
        const bLightness = (b.rgb.r + b.rgb.g + b.rgb.b) / 3
        return bLightness - aLightness
      })
      break
    case 'name':
    default:
      filtered.sort((a, b) => a.name.localeCompare(b.name))
      break
  }

  // 分页
  const start = (currentPage.value - 1) * pageSize
  const end = start + pageSize
  return filtered.slice(start, end)
})

const totalPages = computed(() => {
  let filtered = allColors.value.filter(color => color.brand === activeBrand.value)
  
  if (colorCategory.value !== 'all') {
    filtered = filtered.filter(color => color.category === colorCategory.value)
  }
  
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    filtered = filtered.filter(color => 
      color.name.toLowerCase().includes(query) ||
      color.code.toLowerCase().includes(query) ||
      color.hex.toLowerCase().includes(query)
    )
  }
  
  return Math.ceil(filtered.length / pageSize)
})

const visiblePages = computed(() => {
  const pages = []
  const start = Math.max(1, currentPage.value - 2)
  const end = Math.min(totalPages.value, start + 4)
  
  for (let i = start; i <= end; i++) {
    pages.push(i)
  }
  
  return pages
})

const viewColor = (color: Color) => {
  router.push(`/color/${color.hex.replace('#', '')}`)
}

const copyColor = async (hex: string) => {
  try {
    await navigator.clipboard.writeText(hex)
    console.log('已复制颜色:', hex)
  } catch (err) {
    console.error('复制失败:', err)
  }
}

const likeColor = (color: Color) => {
  color.isLiked = !color.isLiked
}

const goBack = () => {
  router.back()
}

// 监听品牌切换，重置页码
watch(activeBrand, () => {
  currentPage.value = 1
})

watch([colorCategory, searchQuery], () => {
  currentPage.value = 1
})

onMounted(() => {
  // 初始化数据
})
</script>

<style scoped>
.standard-container {
  min-height: 100vh;
  background: #f5f5f5;
}

.header {
  background: white;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  padding: 16px 20px;
  position: sticky;
  top: 0;
  z-index: 100;
}

.nav-container {
  display: flex;
  align-items: center;
  gap: 16px;
  max-width: 1200px;
  margin: 0 auto;
}

.back-button {
  width: 40px;
  height: 40px;
  border: none;
  background: #f0f0f0;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.back-button:hover {
  background: #e0e0e0;
}

.back-button .emoji-icon {
  font-size: 20px;
  color: #666;
}

.page-info h1 {
  margin: 0;
  font-size: 20px;
  color: #333;
}

.page-info p {
  margin: 4px 0 0 0;
  font-size: 14px;
  color: #666;
}

.filter-section {
  background: white;
  border-bottom: 1px solid #e0e0e0;
  padding: 16px 20px;
}

.filter-container {
  max-width: 1200px;
  margin: 0 auto;
}

.brand-filters {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.brand-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 16px;
  border: 1px solid #e0e0e0;
  background: white;
  color: #666;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 14px;
}

.brand-btn.active {
  background: #d32f2f;
  color: white;
  border-color: #d32f2f;
}

.brand-btn:hover {
  border-color: #d32f2f;
}

.brand-btn .emoji-icon {
  font-size: 16px;
}

.color-count {
  font-size: 12px;
  opacity: 0.8;
}

.standard-content {
  padding: 20px;
}

.standard-container-inner {
  max-width: 1200px;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.brand-intro {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.brand-header {
  display: flex;
  align-items: center;
  gap: 20px;
}

.brand-logo {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background: linear-gradient(135deg, #d32f2f, #f44336);
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.brand-logo .emoji-icon {
  font-size: 32px;
}

.brand-details h2 {
  margin: 0 0 8px 0;
  font-size: 24px;
  color: #333;
}

.brand-details p {
  margin: 0 0 12px 0;
  color: #666;
  line-height: 1.5;
}

.brand-stats {
  display: flex;
  gap: 20px;
  flex-wrap: wrap;
}

.stat {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 14px;
  color: #666;
}

.stat .emoji-icon {
  font-size: 14px;
}

.search-section {
  background: white;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.search-controls {
  display: flex;
  gap: 16px;
  align-items: center;
  flex-wrap: wrap;
}

.search-input-group {
  display: flex;
  flex: 1;
  min-width: 300px;
}

.search-input {
  flex: 1;
  padding: 12px 16px;
  border: 1px solid #e0e0e0;
  border-radius: 8px 0 0 8px;
  font-size: 14px;
  outline: none;
}

.search-input:focus {
  border-color: #d32f2f;
}

.search-btn {
  padding: 12px 16px;
  border: 1px solid #d32f2f;
  background: #d32f2f;
  color: white;
  border-radius: 0 8px 8px 0;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.search-btn:hover {
  background: #b71c1c;
}

.filter-controls {
  display: flex;
  gap: 12px;
}

.filter-select {
  padding: 12px 16px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  background: white;
  color: #666;
  font-size: 14px;
  cursor: pointer;
}

.colors-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 16px;
}

.color-item {
  background: white;
  border-radius: 12px;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.color-item:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.color-swatch {
  height: 100px;
  width: 100%;
}

.color-info {
  padding: 12px;
}

.color-name {
  margin: 0 0 4px 0;
  font-size: 14px;
  color: #333;
  font-weight: 600;
}

.color-code {
  margin: 0 0 4px 0;
  font-size: 12px;
  color: #d32f2f;
  font-family: 'Courier New', monospace;
}

.color-hex {
  margin: 0 0 8px 0;
  font-size: 12px;
  color: #666;
  font-family: 'Courier New', monospace;
}

.color-values {
  font-size: 11px;
  color: #999;
  font-family: 'Courier New', monospace;
}

.color-actions {
  display: flex;
  gap: 8px;
  padding: 0 12px 12px;
}

.action-btn {
  width: 32px;
  height: 32px;
  border: 1px solid #e0e0e0;
  background: white;
  border-radius: 6px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.action-btn:hover {
  border-color: #d32f2f;
  color: #d32f2f;
}

.action-btn .emoji-icon {
  font-size: 14px;
}

.pagination {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  margin-top: 24px;
}

.page-btn {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 16px;
  border: 1px solid #e0e0e0;
  background: white;
  color: #666;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.3s ease;
}

.page-btn:hover:not(:disabled) {
  border-color: #d32f2f;
  color: #d32f2f;
}

.page-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.page-numbers {
  display: flex;
  gap: 4px;
}

.page-number {
  width: 36px;
  height: 36px;
  border: 1px solid #e0e0e0;
  background: white;
  color: #666;
  border-radius: 6px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  transition: all 0.3s ease;
}

.page-number.active {
  background: #d32f2f;
  color: white;
  border-color: #d32f2f;
}

.page-number:hover {
  border-color: #d32f2f;
  color: #d32f2f;
}

.page-number.active:hover {
  color: white;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .standard-content {
    padding: 16px;
  }
  
  .colors-grid {
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
  }
  
  .brand-header {
    flex-direction: column;
    text-align: center;
  }
  
  .brand-stats {
    justify-content: center;
  }
  
  .search-controls {
    flex-direction: column;
    align-items: stretch;
  }
  
  .search-input-group {
    min-width: auto;
  }
  
  .filter-controls {
    justify-content: center;
  }
  
  .pagination {
    flex-wrap: wrap;
  }
}
</style>

<template>
  <div class="home-container">
    <!-- 顶部导航 -->
    <header class="header">
      <div class="logo-container">
        <div class="logo-icon">
          <span class="emoji-icon">🎨</span>
        </div>
        <span class="logo-text">色彩通</span>
        <span class="logo-subtitle">SECAITONG</span>
      </div>
    </header>

    <!-- 主要内容区域 -->
    <main class="main-content">
      <!-- 搜索区域 -->
      <div class="search-section">
        <!-- 搜索标签 -->
        <div class="search-tabs">
          <button 
            v-for="tab in searchTabs" 
            :key="tab.key"
            :class="['search-tab', { active: activeTab === tab.key }]"
            @click="activeTab = tab.key"
          >
            {{ tab.label }}
          </button>
        </div>

        <!-- 搜索框 -->
        <div class="search-box">
          <input 
            v-model="searchQuery"
            type="text" 
            :placeholder="getPlaceholder"
            class="search-input"
            @keyup.enter="handleSearch"
          />
          <button class="search-button" @click="handleSearch">
            <span class="emoji-icon">🔍</span>
          </button>
        </div>
      </div>

      <!-- 功能按钮区域 -->
      <div class="features-section">
        <div class="feature-buttons">
          <button
            v-for="feature in features"
            :key="feature.key"
            class="feature-button"
            @click="handleFeatureClick(feature)"
          >
            <div class="feature-icon">
              <span class="emoji-icon">{{ getEmojiIcon(feature.icon) }}</span>
            </div>
            <span class="feature-label">{{ feature.label }}</span>
          </button>
        </div>
      </div>
    </main>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useRouter } from 'vue-router'

const router = useRouter()

// 搜索相关
const activeTab = ref('color')
const searchQuery = ref('')

const searchTabs = [
  { key: 'color', label: '色号查询' },
  { key: 'rgb', label: 'RGB查询' },
  { key: 'cmyk', label: 'CMYK查询' },
  { key: 'hex', label: 'HEX查询' },
  { key: 'traditional', label: '传图识色' }
]

// 功能按钮
const features = [
  { key: 'products', label: '产品', icon: 'product', route: '/products' },
  { key: 'colors', label: '色库', icon: 'color', route: '/colors' },
  { key: 'color-number', label: '色号', icon: 'number', route: '/color-number' },
  { key: 'matching', label: '搭配', icon: 'matching', route: '/matching' },
  { key: 'knowledge', label: '知识', icon: 'knowledge', route: '/knowledge' }
]

// 计算属性
const getPlaceholder = computed(() => {
  const placeholders: Record<string, string> = {
    color: '请输入要查询的色号',
    rgb: '请输入RGB值，如：255,0,0',
    cmyk: '请输入CMYK值，如：0,100,100,0',
    hex: '请输入HEX值，如：#FF0000',
    traditional: '请输入传统色彩名称'
  }
  return placeholders[activeTab.value] || '请输入查询内容'
})

// 方法
const handleSearch = () => {
  if (!searchQuery.value.trim()) return

  // 根据不同的搜索类型进行搜索
  console.log(`搜索类型: ${activeTab.value}, 搜索内容: ${searchQuery.value}`)

  // 这里可以调用相应的API或跳转到搜索结果页面
  router.push({
    path: '/search',
    query: {
      type: activeTab.value,
      q: searchQuery.value
    }
  })
}

const handleFeatureClick = (feature: any) => {
  if (feature.route) {
    router.push(feature.route)
  }
}

// 获取emoji图标
const getEmojiIcon = (iconType: string) => {
  const emojiIcons: Record<string, string> = {
    product: '📦',
    color: '🎨',
    number: '🏷️',
    matching: '❤️',
    knowledge: '📚'
  }
  return emojiIcons[iconType] || emojiIcons.product
}
</script>

<style scoped>
.home-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  display: flex;
  flex-direction: column;
}

.header {
  padding: 20px 0;
  text-align: center;
}

.logo-container {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
}

.logo-icon {
  width: 48px;
  height: 48px;
  color: #d32f2f;
  display: flex;
  align-items: center;
  justify-content: center;
}

.logo-icon .emoji-icon {
  font-size: 48px;
}

.logo-text {
  font-size: 28px;
  font-weight: bold;
  color: #d32f2f;
}

.logo-subtitle {
  font-size: 16px;
  color: #666;
  font-weight: 500;
}

.main-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 40px 20px;
  max-width: 800px;
  margin: 0 auto;
  width: 100%;
}

.search-section {
  width: 100%;
  margin-bottom: 60px;
}

.search-tabs {
  display: flex;
  justify-content: center;
  gap: 8px;
  margin-bottom: 20px;
  flex-wrap: wrap;
}

.search-tab {
  padding: 8px 16px;
  border: none;
  background: #fff;
  color: #666;
  border-radius: 20px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 14px;
}

.search-tab.active {
  background: #d32f2f;
  color: white;
}

.search-tab:hover {
  background: #f0f0f0;
}

.search-tab.active:hover {
  background: #b71c1c;
}

.search-box {
  position: relative;
  max-width: 600px;
  margin: 0 auto;
}

.search-input {
  width: 100%;
  padding: 16px 60px 16px 20px;
  border: 2px solid #e0e0e0;
  border-radius: 25px;
  font-size: 16px;
  outline: none;
  transition: border-color 0.3s ease;
}

.search-input:focus {
  border-color: #d32f2f;
}

.search-button {
  position: absolute;
  right: 8px;
  top: 50%;
  transform: translateY(-50%);
  width: 40px;
  height: 40px;
  border: none;
  background: #d32f2f;
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.3s ease;
}

.search-button:hover {
  background: #b71c1c;
}

.search-button .emoji-icon {
  font-size: 20px;
  color: white;
}

.features-section {
  width: 100%;
}

.feature-buttons {
  display: flex;
  justify-content: center;
  gap: 40px;
  flex-wrap: wrap;
}

.feature-button {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
  padding: 20px;
  border: none;
  background: none;
  cursor: pointer;
  transition: transform 0.3s ease;
}

.feature-button:hover {
  transform: translateY(-5px);
}

.feature-icon {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background: #d32f2f;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  transition: all 0.3s ease;
}

.feature-icon .emoji-icon {
  font-size: 32px;
}

.feature-button:hover .feature-icon {
  background: #b71c1c;
  box-shadow: 0 8px 25px rgba(211, 47, 47, 0.3);
}

.feature-label {
  font-size: 16px;
  font-weight: 500;
  color: #333;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .main-content {
    padding: 20px 15px;
  }
  
  .feature-buttons {
    gap: 20px;
  }
  
  .feature-icon {
    width: 60px;
    height: 60px;
    font-size: 24px;
  }
  
  .search-tabs {
    gap: 4px;
  }
  
  .search-tab {
    padding: 6px 12px;
    font-size: 12px;
  }
}
</style>

<template>
  <div class="home-container">
    <!-- 顶部导航 -->
    <header class="header">
      <div class="logo-container">
        <div class="logo-icon">
          <svg viewBox="0 0 24 24" fill="currentColor">
            <path d="M12 2C6.48 2 2 6.48 2 12S6.48 22 12 22 22 17.52 22 12 17.52 2 12 2ZM12 20C7.59 20 4 16.41 4 12S7.59 4 12 4 20 7.59 20 12 16.41 20 12 20ZM12 6C8.69 6 6 8.69 6 12S8.69 18 12 18 18 15.31 18 12 15.31 6 12 6ZM12 16C9.79 16 8 14.21 8 12S9.79 8 12 8 16 9.79 16 12 14.21 16 12 16Z"/>
          </svg>
        </div>
        <span class="logo-text">色彩通</span>
        <span class="logo-subtitle">SECAITONG</span>
      </div>
    </header>

    <!-- 主要内容区域 -->
    <main class="main-content">
      <!-- 搜索区域 -->
      <div class="search-section">
        <!-- 搜索标签 -->
        <div class="search-tabs">
          <button 
            v-for="tab in searchTabs" 
            :key="tab.key"
            :class="['search-tab', { active: activeTab === tab.key }]"
            @click="activeTab = tab.key"
          >
            {{ tab.label }}
          </button>
        </div>

        <!-- 搜索框 -->
        <div class="search-box">
          <input 
            v-model="searchQuery"
            type="text" 
            :placeholder="getPlaceholder"
            class="search-input"
            @keyup.enter="handleSearch"
          />
          <button class="search-button" @click="handleSearch">
            <svg class="search-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
              <circle cx="11" cy="11" r="8"></circle>
              <path d="m21 21-4.35-4.35"></path>
            </svg>
          </button>
        </div>
      </div>

      <!-- 功能按钮区域 -->
      <div class="features-section">
        <div class="feature-buttons">
          <button
            v-for="feature in features"
            :key="feature.key"
            class="feature-button"
            @click="handleFeatureClick(feature)"
          >
            <div class="feature-icon">
              <svg viewBox="0 0 24 24" fill="currentColor">
                <path :d="getIconSvg(feature.icon)" />
              </svg>
            </div>
            <span class="feature-label">{{ feature.label }}</span>
          </button>
        </div>
      </div>
    </main>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useRouter } from 'vue-router'

const router = useRouter()

// 搜索相关
const activeTab = ref('color')
const searchQuery = ref('')

const searchTabs = [
  { key: 'color', label: '色号查询' },
  { key: 'rgb', label: 'RGB查询' },
  { key: 'cmyk', label: 'CMYK查询' },
  { key: 'hex', label: 'HEX查询' },
  { key: 'traditional', label: '传统识色' }
]

// 功能按钮
const features = [
  { key: 'products', label: '产品', icon: 'product', route: '/products' },
  { key: 'colors', label: '色库', icon: 'color', route: '/colors' },
  { key: 'color-number', label: '色号', icon: 'number', route: '/color-number' },
  { key: 'matching', label: '搭配', icon: 'matching', route: '/matching' },
  { key: 'knowledge', label: '知识', icon: 'knowledge', route: '/knowledge' }
]

// 计算属性
const getPlaceholder = computed(() => {
  const placeholders: Record<string, string> = {
    color: '请输入要查询的色号',
    rgb: '请输入RGB值，如：255,0,0',
    cmyk: '请输入CMYK值，如：0,100,100,0',
    hex: '请输入HEX值，如：#FF0000',
    traditional: '请输入传统色彩名称'
  }
  return placeholders[activeTab.value] || '请输入查询内容'
})

// 方法
const handleSearch = () => {
  if (!searchQuery.value.trim()) return

  // 根据不同的搜索类型进行搜索
  console.log(`搜索类型: ${activeTab.value}, 搜索内容: ${searchQuery.value}`)

  // 这里可以调用相应的API或跳转到搜索结果页面
  router.push({
    path: '/search',
    query: {
      type: activeTab.value,
      q: searchQuery.value
    }
  })
}

const handleFeatureClick = (feature: any) => {
  if (feature.route) {
    router.push(feature.route)
  }
}

// 获取图标SVG
const getIconSvg = (iconType: string) => {
  const icons: Record<string, string> = {
    product: 'M12 2L2 7V17L12 22L22 17V7L12 2ZM12 4.14L19.25 8L12 11.86L4.75 8L12 4.14ZM4 9.45L11 13.31V20.55L4 16.69V9.45ZM13 20.55V13.31L20 9.45V16.69L13 20.55Z',
    color: 'M12 2C13.1 2 14 2.9 14 4C14 5.1 13.1 6 12 6C10.9 6 10 5.1 10 4C10 2.9 10.9 2 12 2ZM21 9V7L15 1H5C3.89 1 3 1.89 3 3V21C3 22.11 3.89 23 5 23H19C20.11 23 21 22.11 21 21V9M19 21H5V3H13V9H19V21Z',
    number: 'M7 5H9V7H7V9H5V7H3V5H5V3H7V5ZM21 5V7H11V5H21ZM21 11V13H11V11H21ZM21 17V19H11V17H21ZM9 11H7V9H5V11H3V13H5V15H7V13H9V11ZM7 17H9V19H7V21H5V19H3V17H5V15H7V17Z',
    matching: 'M12 2C6.48 2 2 6.48 2 12S6.48 22 12 22 22 17.52 22 12 17.52 2 12 2ZM12 20C7.59 20 4 16.41 4 12S7.59 4 12 4 20 7.59 20 12 16.41 20 12 20ZM12 6C8.69 6 6 8.69 6 12S8.69 18 12 18 18 15.31 18 12 15.31 6 12 6ZM12 16C9.79 16 8 14.21 8 12S9.79 8 12 8 16 9.79 16 12 14.21 16 12 16Z',
    knowledge: 'M19 3H5C3.9 3 3 3.9 3 5V19C3 20.1 3.9 21 5 21H19C20.1 21 21 20.1 21 19V5C21 3.9 20.1 3 19 3ZM19 19H5V5H19V19ZM17 12H7V10H17V12ZM17 16H7V14H17V16ZM17 8H7V6H17V8Z'
  }
  return icons[iconType] || icons.product
}
</script>

<style scoped>
.home-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  display: flex;
  flex-direction: column;
}

.header {
  padding: 20px 0;
  text-align: center;
}

.logo-container {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
}

.logo-icon {
  width: 48px;
  height: 48px;
  color: #d32f2f;
}

.logo-text {
  font-size: 28px;
  font-weight: bold;
  color: #d32f2f;
}

.logo-subtitle {
  font-size: 16px;
  color: #666;
  font-weight: 500;
}

.main-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 40px 20px;
  max-width: 800px;
  margin: 0 auto;
  width: 100%;
}

.search-section {
  width: 100%;
  margin-bottom: 60px;
}

.search-tabs {
  display: flex;
  justify-content: center;
  gap: 8px;
  margin-bottom: 20px;
  flex-wrap: wrap;
}

.search-tab {
  padding: 8px 16px;
  border: none;
  background: #fff;
  color: #666;
  border-radius: 20px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 14px;
}

.search-tab.active {
  background: #d32f2f;
  color: white;
}

.search-tab:hover {
  background: #f0f0f0;
}

.search-tab.active:hover {
  background: #b71c1c;
}

.search-box {
  position: relative;
  max-width: 600px;
  margin: 0 auto;
}

.search-input {
  width: 100%;
  padding: 16px 60px 16px 20px;
  border: 2px solid #e0e0e0;
  border-radius: 25px;
  font-size: 16px;
  outline: none;
  transition: border-color 0.3s ease;
}

.search-input:focus {
  border-color: #d32f2f;
}

.search-button {
  position: absolute;
  right: 8px;
  top: 50%;
  transform: translateY(-50%);
  width: 40px;
  height: 40px;
  border: none;
  background: #d32f2f;
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.3s ease;
}

.search-button:hover {
  background: #b71c1c;
}

.search-icon {
  width: 20px;
  height: 20px;
  color: white;
}

.features-section {
  width: 100%;
}

.feature-buttons {
  display: flex;
  justify-content: center;
  gap: 40px;
  flex-wrap: wrap;
}

.feature-button {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
  padding: 20px;
  border: none;
  background: none;
  cursor: pointer;
  transition: transform 0.3s ease;
}

.feature-button:hover {
  transform: translateY(-5px);
}

.feature-icon {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background: #d32f2f;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 32px;
  transition: all 0.3s ease;
}

.feature-button:hover .feature-icon {
  background: #b71c1c;
  box-shadow: 0 8px 25px rgba(211, 47, 47, 0.3);
}

.feature-label {
  font-size: 16px;
  font-weight: 500;
  color: #333;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .main-content {
    padding: 20px 15px;
  }
  
  .feature-buttons {
    gap: 20px;
  }
  
  .feature-icon {
    width: 60px;
    height: 60px;
    font-size: 24px;
  }
  
  .search-tabs {
    gap: 4px;
  }
  
  .search-tab {
    padding: 6px 12px;
    font-size: 12px;
  }
}
</style>

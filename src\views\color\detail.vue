<template>
  <div class="color-detail-container">
    <!-- 顶部导航 -->
    <header class="header">
      <div class="nav-container">
        <button class="back-button" @click="goBack">
          <span class="emoji-icon">←</span>
        </button>
        <div class="color-info">
          <h1>{{ colorData.name || '颜色详情' }}</h1>
          <p>{{ colorData.code || colorData.hex }}</p>
        </div>
      </div>
    </header>

    <!-- 主要内容 -->
    <main class="detail-content">
      <div class="detail-container">
        <!-- 颜色预览区域 -->
        <div class="color-preview-section">
          <div class="color-preview" :style="{ backgroundColor: colorData.hex }">
            <div class="color-overlay">
              <h2 class="color-name">{{ colorData.name }}</h2>
              <p class="color-code">{{ colorData.code }}</p>
            </div>
          </div>
        </div>

        <!-- 颜色信息卡片 -->
        <div class="color-info-cards">
          <!-- HEX 信息 -->
          <div class="info-card">
            <div class="card-header">
              <span class="emoji-icon">🎨</span>
              <h3>HEX</h3>
            </div>
            <div class="card-content">
              <div class="color-value">{{ colorData.hex }}</div>
              <button class="copy-button" @click="copyToClipboard(colorData.hex)">
                <span class="emoji-icon">📋</span>
                复制
              </button>
            </div>
          </div>

          <!-- RGB 信息 -->
          <div class="info-card">
            <div class="card-header">
              <span class="emoji-icon">🌈</span>
              <h3>RGB</h3>
            </div>
            <div class="card-content">
              <div class="color-value">{{ formatRGB(colorData.rgb) }}</div>
              <button class="copy-button" @click="copyToClipboard(formatRGB(colorData.rgb))">
                <span class="emoji-icon">📋</span>
                复制
              </button>
            </div>
          </div>

          <!-- CMYK 信息 -->
          <div class="info-card">
            <div class="card-header">
              <span class="emoji-icon">🖨️</span>
              <h3>CMYK</h3>
            </div>
            <div class="card-content">
              <div class="color-value">{{ formatCMYK(colorData.cmyk) }}</div>
              <button class="copy-button" @click="copyToClipboard(formatCMYK(colorData.cmyk))">
                <span class="emoji-icon">📋</span>
                复制
              </button>
            </div>
          </div>

          <!-- HSL 信息 -->
          <div class="info-card">
            <div class="card-header">
              <span class="emoji-icon">🎯</span>
              <h3>HSL</h3>
            </div>
            <div class="card-content">
              <div class="color-value">{{ formatHSL(colorData.hsl) }}</div>
              <button class="copy-button" @click="copyToClipboard(formatHSL(colorData.hsl))">
                <span class="emoji-icon">📋</span>
                复制
              </button>
            </div>
          </div>
        </div>

        <!-- 颜色描述 -->
        <div class="color-description" v-if="colorData.description">
          <h3>颜色描述</h3>
          <p>{{ colorData.description }}</p>
        </div>

        <!-- 相似颜色 -->
        <div class="similar-colors" v-if="similarColors.length > 0">
          <h3>相似颜色</h3>
          <div class="similar-colors-grid">
            <div 
              v-for="color in similarColors" 
              :key="color.id"
              class="similar-color-item"
              @click="viewColor(color)"
            >
              <div class="similar-color-preview" :style="{ backgroundColor: color.hex }"></div>
              <div class="similar-color-info">
                <p class="similar-color-name">{{ color.name }}</p>
                <p class="similar-color-code">{{ color.code || color.hex }}</p>
              </div>
            </div>
          </div>
        </div>

        <!-- 操作按钮 -->
        <div class="action-buttons">
          <button class="action-button primary" @click="addToFavorites">
            <span class="emoji-icon">❤️</span>
            收藏颜色
          </button>
          <button class="action-button secondary" @click="shareColor">
            <span class="emoji-icon">📤</span>
            分享颜色
          </button>
          <button class="action-button secondary" @click="downloadColor">
            <span class="emoji-icon">💾</span>
            下载色卡
          </button>
        </div>
      </div>
    </main>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'

interface ColorData {
  id?: number
  name: string
  code?: string
  hex: string
  rgb: { r: number; g: number; b: number }
  cmyk: { c: number; m: number; y: number; k: number }
  hsl: { h: number; s: number; l: number }
  description?: string
}

const route = useRoute()
const router = useRouter()

const colorData = ref<ColorData>({
  name: '中国红',
  code: 'C0M100Y100K0',
  hex: '#DC143C',
  rgb: { r: 220, g: 20, b: 60 },
  cmyk: { c: 0, m: 91, y: 73, k: 14 },
  hsl: { h: 348, s: 83, l: 47 },
  description: '传统中国红色，象征喜庆和吉祥，常用于节庆装饰和传统文化表达。'
})

const similarColors = ref([
  {
    id: 2,
    name: '深红',
    hex: '#8B0000',
    code: 'C30M100Y100K20'
  },
  {
    id: 3,
    name: '玫瑰红',
    hex: '#FF1493',
    code: 'C0M92Y0K0'
  },
  {
    id: 4,
    name: '樱桃红',
    hex: '#DE3163',
    code: 'C0M78Y55K13'
  }
])

onMounted(() => {
  // 根据路由参数加载颜色数据
  const colorParam = route.params.color as string
  if (colorParam) {
    loadColorData(colorParam)
  }
})

const loadColorData = (colorParam: string) => {
  // 这里可以根据参数从API加载真实数据
  // 目前使用模拟数据
  console.log('Loading color data for:', colorParam)
}

const formatRGB = (rgb: { r: number; g: number; b: number }) => {
  return `rgb(${rgb.r}, ${rgb.g}, ${rgb.b})`
}

const formatCMYK = (cmyk: { c: number; m: number; y: number; k: number }) => {
  return `C${cmyk.c} M${cmyk.m} Y${cmyk.y} K${cmyk.k}`
}

const formatHSL = (hsl: { h: number; s: number; l: number }) => {
  return `hsl(${hsl.h}, ${hsl.s}%, ${hsl.l}%)`
}

const copyToClipboard = async (text: string) => {
  try {
    await navigator.clipboard.writeText(text)
    // 这里可以添加成功提示
    console.log('已复制到剪贴板:', text)
  } catch (err) {
    console.error('复制失败:', err)
  }
}

const goBack = () => {
  router.back()
}

const viewColor = (color: any) => {
  router.push(`/color/${color.hex.replace('#', '')}`)
}

const addToFavorites = () => {
  // 添加到收藏夹的逻辑
  console.log('添加到收藏夹')
}

const shareColor = () => {
  // 分享颜色的逻辑
  if (navigator.share) {
    navigator.share({
      title: `${colorData.value.name} - 色彩通`,
      text: `查看这个美丽的颜色：${colorData.value.name} (${colorData.value.hex})`,
      url: window.location.href
    })
  } else {
    copyToClipboard(window.location.href)
  }
}

const downloadColor = () => {
  // 下载色卡的逻辑
  console.log('下载色卡')
}
</script>

<style scoped>
.color-detail-container {
  min-height: 100vh;
  background: #f5f5f5;
}

.header {
  background: white;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  padding: 16px 20px;
  position: sticky;
  top: 0;
  z-index: 100;
}

.nav-container {
  display: flex;
  align-items: center;
  gap: 16px;
  max-width: 800px;
  margin: 0 auto;
}

.back-button {
  width: 40px;
  height: 40px;
  border: none;
  background: #f0f0f0;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.back-button:hover {
  background: #e0e0e0;
}

.back-button .emoji-icon {
  font-size: 20px;
  color: #666;
}

.color-info h1 {
  margin: 0;
  font-size: 20px;
  color: #333;
}

.color-info p {
  margin: 4px 0 0 0;
  font-size: 14px;
  color: #666;
}

.detail-content {
  padding: 20px;
}

.detail-container {
  max-width: 800px;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.color-preview-section {
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 4px 16px rgba(0,0,0,0.1);
}

.color-preview {
  height: 200px;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.color-overlay {
  text-align: center;
  color: white;
  text-shadow: 0 2px 4px rgba(0,0,0,0.5);
}

.color-name {
  margin: 0 0 8px 0;
  font-size: 28px;
  font-weight: bold;
}

.color-code {
  margin: 0;
  font-size: 16px;
  opacity: 0.9;
}

.color-info-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
}

.info-card {
  background: white;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.card-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 12px;
}

.card-header .emoji-icon {
  font-size: 20px;
}

.card-header h3 {
  margin: 0;
  font-size: 16px;
  color: #333;
}

.card-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 12px;
}

.color-value {
  font-family: 'Courier New', monospace;
  font-size: 14px;
  color: #666;
  flex: 1;
}

.copy-button {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 6px 12px;
  border: 1px solid #ddd;
  background: #f9f9f9;
  border-radius: 6px;
  cursor: pointer;
  font-size: 12px;
  color: #666;
  transition: all 0.3s ease;
}

.copy-button:hover {
  background: #f0f0f0;
  border-color: #ccc;
}

.copy-button .emoji-icon {
  font-size: 12px;
}

.color-description {
  background: white;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.color-description h3 {
  margin: 0 0 12px 0;
  font-size: 18px;
  color: #333;
}

.color-description p {
  margin: 0;
  line-height: 1.6;
  color: #666;
}

.similar-colors {
  background: white;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.similar-colors h3 {
  margin: 0 0 16px 0;
  font-size: 18px;
  color: #333;
}

.similar-colors-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
  gap: 12px;
}

.similar-color-item {
  cursor: pointer;
  border-radius: 8px;
  overflow: hidden;
  transition: transform 0.3s ease;
  border: 1px solid #e0e0e0;
}

.similar-color-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.similar-color-preview {
  height: 80px;
  width: 100%;
}

.similar-color-info {
  padding: 12px;
}

.similar-color-name {
  margin: 0 0 4px 0;
  font-size: 14px;
  font-weight: 500;
  color: #333;
}

.similar-color-code {
  margin: 0;
  font-size: 12px;
  color: #666;
}

.action-buttons {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

.action-button {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 20px;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.3s ease;
  flex: 1;
  min-width: 120px;
  justify-content: center;
}

.action-button.primary {
  background: #d32f2f;
  color: white;
}

.action-button.primary:hover {
  background: #b71c1c;
}

.action-button.secondary {
  background: #f5f5f5;
  color: #666;
  border: 1px solid #ddd;
}

.action-button.secondary:hover {
  background: #e0e0e0;
}

.action-button .emoji-icon {
  font-size: 16px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .detail-content {
    padding: 16px;
  }
  
  .color-info-cards {
    grid-template-columns: 1fr;
  }
  
  .action-buttons {
    flex-direction: column;
  }
  
  .action-button {
    flex: none;
  }
}
</style>

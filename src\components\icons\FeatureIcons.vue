<template>
  <component :is="iconName" />
</template>

<script setup lang="ts">
import { defineComponent } from 'vue'

interface Props {
  name: string
}

const props = defineProps<Props>()

// 产品图标
const ProductIcon = defineComponent({
  name: 'ProductIcon',
  render() {
    return (
      <svg viewBox="0 0 24 24" fill="currentColor" style="width: 100%; height: 100%;">
        <path d="M12 2L2 7V17L12 22L22 17V7L12 2ZM12 4.14L19.25 8L12 11.86L4.75 8L12 4.14ZM4 9.45L11 13.31V20.55L4 16.69V9.45ZM13 20.55V13.31L20 9.45V16.69L13 20.55Z"/>
      </svg>
    )
  }
})

// 色库图标
const ColorIcon = defineComponent({
  name: 'ColorIcon',
  render() {
    return (
      <svg viewBox="0 0 24 24" fill="currentColor" style="width: 100%; height: 100%;">
        <path d="M12 2C13.1 2 14 2.9 14 4C14 5.1 13.1 6 12 6C10.9 6 10 5.1 10 4C10 2.9 10.9 2 12 2ZM21 9V7L15 1H5C3.89 1 3 1.89 3 3V21C3 22.11 3.89 23 5 23H19C20.11 23 21 22.11 21 21V9M19 21H5V3H13V9H19V21Z"/>
      </svg>
    )
  }
})

// 色号图标
const NumberIcon = defineComponent({
  name: 'NumberIcon',
  render() {
    return (
      <svg viewBox="0 0 24 24" fill="currentColor" style="width: 100%; height: 100%;">
        <path d="M7 5H9V7H7V9H5V7H3V5H5V3H7V5ZM21 5V7H11V5H21ZM21 11V13H11V11H21ZM21 17V19H11V17H21ZM9 11H7V9H5V11H3V13H5V15H7V13H9V11ZM7 17H9V19H7V21H5V19H3V17H5V15H7V17Z"/>
      </svg>
    )
  }
})

// 搭配图标
const MatchingIcon = defineComponent({
  name: 'MatchingIcon',
  render() {
    return (
      <svg viewBox="0 0 24 24" fill="currentColor" style="width: 100%; height: 100%;">
        <path d="M12 2C6.48 2 2 6.48 2 12S6.48 22 12 22 22 17.52 22 12 17.52 2 12 2ZM12 20C7.59 20 4 16.41 4 12S7.59 4 12 4 20 7.59 20 12 16.41 20 12 20ZM12 6C8.69 6 6 8.69 6 12S8.69 18 12 18 18 15.31 18 12 15.31 6 12 6ZM12 16C9.79 16 8 14.21 8 12S9.79 8 12 8 16 9.79 16 12 14.21 16 12 16Z"/>
      </svg>
    )
  }
})

// 知识图标
const KnowledgeIcon = defineComponent({
  name: 'KnowledgeIcon',
  render() {
    return (
      <svg viewBox="0 0 24 24" fill="currentColor" style="width: 100%; height: 100%;">
        <path d="M19 3H5C3.9 3 3 3.9 3 5V19C3 20.1 3.9 21 5 21H19C20.1 21 21 20.1 21 19V5C21 3.9 20.1 3 19 3ZM19 19H5V5H19V19ZM17 12H7V10H17V12ZM17 16H7V14H17V16ZM17 8H7V6H17V8Z"/>
      </svg>
    )
  }
})

const iconComponents = {
  ProductIcon,
  ColorIcon,
  NumberIcon,
  MatchingIcon,
  KnowledgeIcon
}

const iconName = computed(() => iconComponents[props.name] || ProductIcon)
</script>

<template>
  <div class="convert-container">
    <!-- 顶部导航 -->
    <header class="header">
      <div class="nav-container">
        <button class="back-button" @click="goBack">
          <span class="emoji-icon">←</span>
        </button>
        <div class="page-info">
          <h1>颜色转换工具</h1>
          <p>支持RGB、CMYK、HEX、HSL等格式互转</p>
        </div>
      </div>
    </header>

    <!-- 主要内容 -->
    <main class="convert-content">
      <div class="convert-container-inner">
        <!-- 颜色预览 -->
        <div class="color-preview-section">
          <div class="color-preview" :style="{ backgroundColor: currentColor }">
            <div class="color-overlay">
              <h2>当前颜色</h2>
              <p>{{ currentColor }}</p>
            </div>
          </div>
        </div>

        <!-- 转换工具 -->
        <div class="convert-tools">
          <!-- HEX 输入 -->
          <div class="convert-card">
            <div class="card-header">
              <span class="emoji-icon">🎨</span>
              <h3>HEX</h3>
            </div>
            <div class="card-content">
              <input 
                v-model="hexValue" 
                type="text" 
                placeholder="#FF0000"
                @input="onHexChange"
                class="color-input"
              />
              <button class="copy-button" @click="copyToClipboard(hexValue)">
                <span class="emoji-icon">📋</span>
              </button>
            </div>
          </div>

          <!-- RGB 输入 -->
          <div class="convert-card">
            <div class="card-header">
              <span class="emoji-icon">🌈</span>
              <h3>RGB</h3>
            </div>
            <div class="card-content">
              <div class="rgb-inputs">
                <div class="rgb-input-group">
                  <label>R</label>
                  <input 
                    v-model.number="rgbValue.r" 
                    type="number" 
                    min="0" 
                    max="255"
                    @input="onRgbChange"
                    class="rgb-input"
                  />
                </div>
                <div class="rgb-input-group">
                  <label>G</label>
                  <input 
                    v-model.number="rgbValue.g" 
                    type="number" 
                    min="0" 
                    max="255"
                    @input="onRgbChange"
                    class="rgb-input"
                  />
                </div>
                <div class="rgb-input-group">
                  <label>B</label>
                  <input 
                    v-model.number="rgbValue.b" 
                    type="number" 
                    min="0" 
                    max="255"
                    @input="onRgbChange"
                    class="rgb-input"
                  />
                </div>
              </div>
              <button class="copy-button" @click="copyToClipboard(`rgb(${rgbValue.r}, ${rgbValue.g}, ${rgbValue.b})`)">
                <span class="emoji-icon">📋</span>
              </button>
            </div>
          </div>

          <!-- CMYK 输入 -->
          <div class="convert-card">
            <div class="card-header">
              <span class="emoji-icon">🖨️</span>
              <h3>CMYK</h3>
            </div>
            <div class="card-content">
              <div class="cmyk-inputs">
                <div class="cmyk-input-group">
                  <label>C</label>
                  <input 
                    v-model.number="cmykValue.c" 
                    type="number" 
                    min="0" 
                    max="100"
                    @input="onCmykChange"
                    class="cmyk-input"
                  />
                </div>
                <div class="cmyk-input-group">
                  <label>M</label>
                  <input 
                    v-model.number="cmykValue.m" 
                    type="number" 
                    min="0" 
                    max="100"
                    @input="onCmykChange"
                    class="cmyk-input"
                  />
                </div>
                <div class="cmyk-input-group">
                  <label>Y</label>
                  <input 
                    v-model.number="cmykValue.y" 
                    type="number" 
                    min="0" 
                    max="100"
                    @input="onCmykChange"
                    class="cmyk-input"
                  />
                </div>
                <div class="cmyk-input-group">
                  <label>K</label>
                  <input 
                    v-model.number="cmykValue.k" 
                    type="number" 
                    min="0" 
                    max="100"
                    @input="onCmykChange"
                    class="cmyk-input"
                  />
                </div>
              </div>
              <button class="copy-button" @click="copyToClipboard(`C${cmykValue.c} M${cmykValue.m} Y${cmykValue.y} K${cmykValue.k}`)">
                <span class="emoji-icon">📋</span>
              </button>
            </div>
          </div>

          <!-- HSL 输入 -->
          <div class="convert-card">
            <div class="card-header">
              <span class="emoji-icon">🎯</span>
              <h3>HSL</h3>
            </div>
            <div class="card-content">
              <div class="hsl-inputs">
                <div class="hsl-input-group">
                  <label>H</label>
                  <input 
                    v-model.number="hslValue.h" 
                    type="number" 
                    min="0" 
                    max="360"
                    @input="onHslChange"
                    class="hsl-input"
                  />
                </div>
                <div class="hsl-input-group">
                  <label>S</label>
                  <input 
                    v-model.number="hslValue.s" 
                    type="number" 
                    min="0" 
                    max="100"
                    @input="onHslChange"
                    class="hsl-input"
                  />
                </div>
                <div class="hsl-input-group">
                  <label>L</label>
                  <input 
                    v-model.number="hslValue.l" 
                    type="number" 
                    min="0" 
                    max="100"
                    @input="onHslChange"
                    class="hsl-input"
                  />
                </div>
              </div>
              <button class="copy-button" @click="copyToClipboard(`hsl(${hslValue.h}, ${hslValue.s}%, ${hslValue.l}%)`)">
                <span class="emoji-icon">📋</span>
              </button>
            </div>
          </div>
        </div>

        <!-- 快速选择颜色 -->
        <div class="quick-colors">
          <h3>快速选择</h3>
          <div class="quick-colors-grid">
            <div 
              v-for="color in quickColors" 
              :key="color.hex"
              class="quick-color-item"
              :style="{ backgroundColor: color.hex }"
              @click="selectQuickColor(color.hex)"
              :title="color.name"
            ></div>
          </div>
        </div>

        <!-- 操作按钮 -->
        <div class="action-buttons">
          <button class="action-button primary" @click="resetColors">
            <span class="emoji-icon">🔄</span>
            重置
          </button>
          <button class="action-button secondary" @click="randomColor">
            <span class="emoji-icon">🎲</span>
            随机颜色
          </button>
          <button class="action-button secondary" @click="saveColor">
            <span class="emoji-icon">💾</span>
            保存颜色
          </button>
        </div>
      </div>
    </main>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'

const router = useRouter()

// 颜色值
const hexValue = ref('#FF0000')
const rgbValue = ref({ r: 255, g: 0, b: 0 })
const cmykValue = ref({ c: 0, m: 100, y: 100, k: 0 })
const hslValue = ref({ h: 0, s: 100, l: 50 })

// 当前显示的颜色
const currentColor = computed(() => hexValue.value)

// 快速选择的颜色
const quickColors = ref([
  { name: '红色', hex: '#FF0000' },
  { name: '绿色', hex: '#00FF00' },
  { name: '蓝色', hex: '#0000FF' },
  { name: '黄色', hex: '#FFFF00' },
  { name: '紫色', hex: '#800080' },
  { name: '橙色', hex: '#FFA500' },
  { name: '粉色', hex: '#FFC0CB' },
  { name: '青色', hex: '#00FFFF' },
  { name: '黑色', hex: '#000000' },
  { name: '白色', hex: '#FFFFFF' },
  { name: '灰色', hex: '#808080' },
  { name: '棕色', hex: '#A52A2A' }
])

onMounted(() => {
  // 初始化颜色转换
  updateAllFromHex('#FF0000')
})

// 颜色转换函数
const hexToRgb = (hex: string) => {
  const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex)
  return result ? {
    r: parseInt(result[1], 16),
    g: parseInt(result[2], 16),
    b: parseInt(result[3], 16)
  } : { r: 0, g: 0, b: 0 }
}

const rgbToHex = (r: number, g: number, b: number) => {
  return "#" + ((1 << 24) + (r << 16) + (g << 8) + b).toString(16).slice(1)
}

const rgbToCmyk = (r: number, g: number, b: number) => {
  const rPercent = r / 255
  const gPercent = g / 255
  const bPercent = b / 255
  
  const k = 1 - Math.max(rPercent, gPercent, bPercent)
  const c = (1 - rPercent - k) / (1 - k) || 0
  const m = (1 - gPercent - k) / (1 - k) || 0
  const y = (1 - bPercent - k) / (1 - k) || 0
  
  return {
    c: Math.round(c * 100),
    m: Math.round(m * 100),
    y: Math.round(y * 100),
    k: Math.round(k * 100)
  }
}

const cmykToRgb = (c: number, m: number, y: number, k: number) => {
  const r = 255 * (1 - c / 100) * (1 - k / 100)
  const g = 255 * (1 - m / 100) * (1 - k / 100)
  const b = 255 * (1 - y / 100) * (1 - k / 100)
  
  return {
    r: Math.round(r),
    g: Math.round(g),
    b: Math.round(b)
  }
}

const rgbToHsl = (r: number, g: number, b: number) => {
  r /= 255
  g /= 255
  b /= 255
  
  const max = Math.max(r, g, b)
  const min = Math.min(r, g, b)
  let h = 0, s = 0, l = (max + min) / 2
  
  if (max !== min) {
    const d = max - min
    s = l > 0.5 ? d / (2 - max - min) : d / (max + min)
    
    switch (max) {
      case r: h = (g - b) / d + (g < b ? 6 : 0); break
      case g: h = (b - r) / d + 2; break
      case b: h = (r - g) / d + 4; break
    }
    h /= 6
  }
  
  return {
    h: Math.round(h * 360),
    s: Math.round(s * 100),
    l: Math.round(l * 100)
  }
}

const hslToRgb = (h: number, s: number, l: number) => {
  h /= 360
  s /= 100
  l /= 100
  
  const hue2rgb = (p: number, q: number, t: number) => {
    if (t < 0) t += 1
    if (t > 1) t -= 1
    if (t < 1/6) return p + (q - p) * 6 * t
    if (t < 1/2) return q
    if (t < 2/3) return p + (q - p) * (2/3 - t) * 6
    return p
  }
  
  let r, g, b
  
  if (s === 0) {
    r = g = b = l
  } else {
    const q = l < 0.5 ? l * (1 + s) : l + s - l * s
    const p = 2 * l - q
    r = hue2rgb(p, q, h + 1/3)
    g = hue2rgb(p, q, h)
    b = hue2rgb(p, q, h - 1/3)
  }
  
  return {
    r: Math.round(r * 255),
    g: Math.round(g * 255),
    b: Math.round(b * 255)
  }
}

// 更新所有颜色格式
const updateAllFromHex = (hex: string) => {
  hexValue.value = hex
  const rgb = hexToRgb(hex)
  rgbValue.value = rgb
  cmykValue.value = rgbToCmyk(rgb.r, rgb.g, rgb.b)
  hslValue.value = rgbToHsl(rgb.r, rgb.g, rgb.b)
}

const updateAllFromRgb = (rgb: { r: number; g: number; b: number }) => {
  rgbValue.value = rgb
  hexValue.value = rgbToHex(rgb.r, rgb.g, rgb.b)
  cmykValue.value = rgbToCmyk(rgb.r, rgb.g, rgb.b)
  hslValue.value = rgbToHsl(rgb.r, rgb.g, rgb.b)
}

const updateAllFromCmyk = (cmyk: { c: number; m: number; y: number; k: number }) => {
  cmykValue.value = cmyk
  const rgb = cmykToRgb(cmyk.c, cmyk.m, cmyk.y, cmyk.k)
  rgbValue.value = rgb
  hexValue.value = rgbToHex(rgb.r, rgb.g, rgb.b)
  hslValue.value = rgbToHsl(rgb.r, rgb.g, rgb.b)
}

const updateAllFromHsl = (hsl: { h: number; s: number; l: number }) => {
  hslValue.value = hsl
  const rgb = hslToRgb(hsl.h, hsl.s, hsl.l)
  rgbValue.value = rgb
  hexValue.value = rgbToHex(rgb.r, rgb.g, rgb.b)
  cmykValue.value = rgbToCmyk(rgb.r, rgb.g, rgb.b)
}

// 事件处理
const onHexChange = () => {
  if (/^#[0-9A-F]{6}$/i.test(hexValue.value)) {
    updateAllFromHex(hexValue.value)
  }
}

const onRgbChange = () => {
  updateAllFromRgb(rgbValue.value)
}

const onCmykChange = () => {
  updateAllFromCmyk(cmykValue.value)
}

const onHslChange = () => {
  updateAllFromHsl(hslValue.value)
}

const selectQuickColor = (hex: string) => {
  updateAllFromHex(hex)
}

const resetColors = () => {
  updateAllFromHex('#FF0000')
}

const randomColor = () => {
  const randomHex = '#' + Math.floor(Math.random()*16777215).toString(16).padStart(6, '0')
  updateAllFromHex(randomHex)
}

const saveColor = () => {
  // 保存颜色到收藏夹或历史记录
  console.log('保存颜色:', currentColor.value)
}

const copyToClipboard = async (text: string) => {
  try {
    await navigator.clipboard.writeText(text)
    console.log('已复制到剪贴板:', text)
  } catch (err) {
    console.error('复制失败:', err)
  }
}

const goBack = () => {
  router.back()
}
</script>

<style scoped>
.convert-container {
  min-height: 100vh;
  background: #f5f5f5;
}

.header {
  background: white;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  padding: 16px 20px;
  position: sticky;
  top: 0;
  z-index: 100;
}

.nav-container {
  display: flex;
  align-items: center;
  gap: 16px;
  max-width: 800px;
  margin: 0 auto;
}

.back-button {
  width: 40px;
  height: 40px;
  border: none;
  background: #f0f0f0;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.back-button:hover {
  background: #e0e0e0;
}

.back-button .emoji-icon {
  font-size: 20px;
  color: #666;
}

.page-info h1 {
  margin: 0;
  font-size: 20px;
  color: #333;
}

.page-info p {
  margin: 4px 0 0 0;
  font-size: 14px;
  color: #666;
}

.convert-content {
  padding: 20px;
}

.convert-container-inner {
  max-width: 800px;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.color-preview-section {
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 4px 16px rgba(0,0,0,0.1);
}

.color-preview {
  height: 150px;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.3s ease;
}

.color-overlay {
  text-align: center;
  color: white;
  text-shadow: 0 2px 4px rgba(0,0,0,0.5);
}

.color-overlay h2 {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: bold;
}

.color-overlay p {
  margin: 0;
  font-size: 16px;
  font-family: 'Courier New', monospace;
}

.convert-tools {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 16px;
}

.convert-card {
  background: white;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.card-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 16px;
}

.card-header .emoji-icon {
  font-size: 20px;
}

.card-header h3 {
  margin: 0;
  font-size: 16px;
  color: #333;
}

.card-content {
  display: flex;
  align-items: center;
  gap: 12px;
}

.color-input {
  flex: 1;
  padding: 12px;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 14px;
  font-family: 'Courier New', monospace;
}

.rgb-inputs, .cmyk-inputs, .hsl-inputs {
  display: flex;
  gap: 8px;
  flex: 1;
}

.rgb-input-group, .cmyk-input-group, .hsl-input-group {
  display: flex;
  flex-direction: column;
  gap: 4px;
  flex: 1;
}

.rgb-input-group label, .cmyk-input-group label, .hsl-input-group label {
  font-size: 12px;
  color: #666;
  font-weight: 500;
}

.rgb-input, .cmyk-input, .hsl-input {
  padding: 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
  text-align: center;
}

.copy-button {
  width: 40px;
  height: 40px;
  border: 1px solid #ddd;
  background: #f9f9f9;
  border-radius: 6px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  flex-shrink: 0;
}

.copy-button:hover {
  background: #f0f0f0;
  border-color: #ccc;
}

.copy-button .emoji-icon {
  font-size: 16px;
}

.quick-colors {
  background: white;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.quick-colors h3 {
  margin: 0 0 16px 0;
  font-size: 18px;
  color: #333;
}

.quick-colors-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
  gap: 8px;
}

.quick-color-item {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  cursor: pointer;
  border: 2px solid #e0e0e0;
  transition: all 0.3s ease;
}

.quick-color-item:hover {
  transform: scale(1.1);
  border-color: #d32f2f;
}

.action-buttons {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

.action-button {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 20px;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.3s ease;
  flex: 1;
  min-width: 120px;
  justify-content: center;
}

.action-button.primary {
  background: #d32f2f;
  color: white;
}

.action-button.primary:hover {
  background: #b71c1c;
}

.action-button.secondary {
  background: #f5f5f5;
  color: #666;
  border: 1px solid #ddd;
}

.action-button.secondary:hover {
  background: #e0e0e0;
}

.action-button .emoji-icon {
  font-size: 16px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .convert-content {
    padding: 16px;
  }
  
  .convert-tools {
    grid-template-columns: 1fr;
  }
  
  .action-buttons {
    flex-direction: column;
  }
  
  .action-button {
    flex: none;
  }
  
  .rgb-inputs, .cmyk-inputs, .hsl-inputs {
    flex-direction: column;
  }
}
</style>

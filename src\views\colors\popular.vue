<template>
  <div class="popular-container">
    <!-- 顶部导航 -->
    <header class="header">
      <div class="nav-container">
        <button class="back-button" @click="goBack">
          <span class="emoji-icon">←</span>
        </button>
        <div class="page-info">
          <h1>流行色库</h1>
          <p>大量流行色板及色彩趋势，助您灵感获取</p>
        </div>
      </div>
    </header>

    <!-- 筛选和搜索 -->
    <div class="filter-section">
      <div class="filter-container">
        <!-- 分类筛选 -->
        <div class="category-filters">
          <button 
            v-for="category in categories" 
            :key="category.key"
            :class="['category-btn', { active: activeCategory === category.key }]"
            @click="activeCategory = category.key"
          >
            <span class="emoji-icon">{{ category.icon }}</span>
            {{ category.label }}
          </button>
        </div>

        <!-- 排序选项 -->
        <div class="sort-options">
          <select v-model="sortBy" class="sort-select">
            <option value="popular">最受欢迎</option>
            <option value="newest">最新发布</option>
            <option value="likes">点赞最多</option>
            <option value="views">浏览最多</option>
          </select>
        </div>
      </div>
    </div>

    <!-- 色板网格 -->
    <main class="popular-content">
      <div class="popular-container-inner">
        <div class="palettes-grid">
          <div 
            v-for="palette in filteredPalettes" 
            :key="palette.id"
            class="palette-card"
            @click="viewPalette(palette)"
          >
            <!-- 色彩条 -->
            <div class="palette-colors">
              <div 
                v-for="(color, index) in palette.colors" 
                :key="index"
                class="palette-color"
                :style="{ backgroundColor: color }"
                :title="color"
              ></div>
            </div>

            <!-- 色板信息 -->
            <div class="palette-info">
              <h3 class="palette-title">{{ palette.title }}</h3>
              <p class="palette-description">{{ palette.description }}</p>
              
              <!-- 标签 -->
              <div class="palette-tags">
                <span 
                  v-for="tag in palette.tags" 
                  :key="tag"
                  class="palette-tag"
                >
                  {{ tag }}
                </span>
              </div>

              <!-- 统计信息 -->
              <div class="palette-stats">
                <div class="stat-item">
                  <span class="emoji-icon">❤️</span>
                  {{ palette.likes }}
                </div>
                <div class="stat-item">
                  <span class="emoji-icon">👁️</span>
                  {{ palette.views }}
                </div>
                <div class="stat-item">
                  <span class="emoji-icon">💾</span>
                  {{ palette.downloads }}
                </div>
              </div>

              <!-- 操作按钮 -->
              <div class="palette-actions">
                <button class="action-btn like-btn" @click.stop="toggleLike(palette)">
                  <span class="emoji-icon">{{ palette.isLiked ? '❤️' : '🤍' }}</span>
                  {{ palette.isLiked ? '已收藏' : '收藏' }}
                </button>
                <button class="action-btn download-btn" @click.stop="downloadPalette(palette)">
                  <span class="emoji-icon">📥</span>
                  下载
                </button>
                <button class="action-btn share-btn" @click.stop="sharePalette(palette)">
                  <span class="emoji-icon">📤</span>
                  分享
                </button>
              </div>
            </div>
          </div>
        </div>

        <!-- 加载更多 -->
        <div class="load-more" v-if="hasMore">
          <button class="load-more-btn" @click="loadMore" :disabled="loading">
            <span class="emoji-icon">{{ loading ? '⏳' : '📥' }}</span>
            {{ loading ? '加载中...' : '加载更多' }}
          </button>
        </div>
      </div>
    </main>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'

interface Palette {
  id: number
  title: string
  description: string
  colors: string[]
  category: string
  tags: string[]
  likes: number
  views: number
  downloads: number
  isLiked: boolean
  createdAt: string
}

const router = useRouter()

const activeCategory = ref('all')
const sortBy = ref('popular')
const loading = ref(false)
const hasMore = ref(true)

const categories = [
  { key: 'all', label: '全部', icon: '🎨' },
  { key: 'modern', label: '现代简约', icon: '🏢' },
  { key: 'nature', label: '自然田园', icon: '🌿' },
  { key: 'vintage', label: '复古怀旧', icon: '📻' },
  { key: 'romantic', label: '浪漫温馨', icon: '💕' },
  { key: 'professional', label: '商务专业', icon: '💼' },
  { key: 'creative', label: '创意艺术', icon: '🎭' },
  { key: 'seasonal', label: '季节主题', icon: '🍂' }
]

const palettes = ref<Palette[]>([
  {
    id: 1,
    title: '现代极简主义',
    description: '简洁明快的现代设计色彩，适合商务和科技产品',
    colors: ['#2C3E50', '#34495E', '#7F8C8D', '#BDC3C7', '#ECF0F1'],
    category: 'modern',
    tags: ['简约', '商务', '科技'],
    likes: 1234,
    views: 5678,
    downloads: 892,
    isLiked: false,
    createdAt: '2024-01-15'
  },
  {
    id: 2,
    title: '温暖秋日',
    description: '秋天的温暖色调，营造舒适惬意的氛围',
    colors: ['#D35400', '#E67E22', '#F39C12', '#F1C40F', '#F4D03F'],
    category: 'seasonal',
    tags: ['秋天', '温暖', '自然'],
    likes: 987,
    views: 3456,
    downloads: 654,
    isLiked: true,
    createdAt: '2024-01-10'
  },
  {
    id: 3,
    title: '海洋深蓝',
    description: '深邃的海洋色彩，带来宁静和深度感',
    colors: ['#1B4F72', '#2E86AB', '#A23B72', '#F18F01', '#C73E1D'],
    category: 'nature',
    tags: ['海洋', '深邃', '宁静'],
    likes: 756,
    views: 2345,
    downloads: 432,
    isLiked: false,
    createdAt: '2024-01-08'
  },
  {
    id: 4,
    title: '粉色梦境',
    description: '浪漫的粉色系列，适合女性产品和浪漫主题',
    colors: ['#FCE4EC', '#F8BBD9', '#F48FB1', '#F06292', '#E91E63'],
    category: 'romantic',
    tags: ['浪漫', '粉色', '女性'],
    likes: 1456,
    views: 6789,
    downloads: 1023,
    isLiked: true,
    createdAt: '2024-01-05'
  },
  {
    id: 5,
    title: '森林绿意',
    description: '清新的绿色调，象征生机和自然',
    colors: ['#1B5E20', '#2E7D32', '#388E3C', '#4CAF50', '#66BB6A'],
    category: 'nature',
    tags: ['绿色', '自然', '清新'],
    likes: 634,
    views: 1987,
    downloads: 345,
    isLiked: false,
    createdAt: '2024-01-03'
  },
  {
    id: 6,
    title: '复古怀旧',
    description: '经典的复古色彩，带有怀旧和历史感',
    colors: ['#8D6E63', '#A1887F', '#BCAAA4', '#D7CCC8', '#EFEBE9'],
    category: 'vintage',
    tags: ['复古', '怀旧', '经典'],
    likes: 892,
    views: 4321,
    downloads: 567,
    isLiked: false,
    createdAt: '2024-01-01'
  },
  {
    id: 7,
    title: '商务专业',
    description: '稳重的商务色彩，适合企业和正式场合',
    colors: ['#263238', '#37474F', '#455A64', '#546E7A', '#607D8B'],
    category: 'professional',
    tags: ['商务', '专业', '稳重'],
    likes: 543,
    views: 2876,
    downloads: 234,
    isLiked: false,
    createdAt: '2023-12-28'
  },
  {
    id: 8,
    title: '创意彩虹',
    description: '充满活力的彩虹色彩，激发创意灵感',
    colors: ['#E74C3C', '#F39C12', '#F1C40F', '#2ECC71', '#3498DB'],
    category: 'creative',
    tags: ['彩虹', '创意', '活力'],
    likes: 1678,
    views: 8765,
    downloads: 1234,
    isLiked: true,
    createdAt: '2023-12-25'
  }
])

const filteredPalettes = computed(() => {
  let filtered = palettes.value

  // 按分类筛选
  if (activeCategory.value !== 'all') {
    filtered = filtered.filter(palette => palette.category === activeCategory.value)
  }

  // 排序
  switch (sortBy.value) {
    case 'newest':
      filtered.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime())
      break
    case 'likes':
      filtered.sort((a, b) => b.likes - a.likes)
      break
    case 'views':
      filtered.sort((a, b) => b.views - a.views)
      break
    case 'popular':
    default:
      filtered.sort((a, b) => (b.likes + b.views) - (a.likes + a.views))
      break
  }

  return filtered
})

const viewPalette = (palette: Palette) => {
  router.push(`/palette/${palette.id}`)
}

const toggleLike = (palette: Palette) => {
  palette.isLiked = !palette.isLiked
  if (palette.isLiked) {
    palette.likes++
  } else {
    palette.likes--
  }
}

const downloadPalette = (palette: Palette) => {
  palette.downloads++
  console.log('下载色板:', palette.title)
  // 实现下载逻辑
}

const sharePalette = (palette: Palette) => {
  if (navigator.share) {
    navigator.share({
      title: palette.title,
      text: palette.description,
      url: `${window.location.origin}/palette/${palette.id}`
    })
  } else {
    // 复制链接到剪贴板
    navigator.clipboard.writeText(`${window.location.origin}/palette/${palette.id}`)
  }
}

const loadMore = () => {
  loading.value = true
  // 模拟加载更多数据
  setTimeout(() => {
    loading.value = false
    hasMore.value = false // 假设没有更多数据
  }, 1000)
}

const goBack = () => {
  router.back()
}

onMounted(() => {
  // 初始化数据
})
</script>

<style scoped>
.popular-container {
  min-height: 100vh;
  background: #f5f5f5;
}

.header {
  background: white;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  padding: 16px 20px;
  position: sticky;
  top: 0;
  z-index: 100;
}

.nav-container {
  display: flex;
  align-items: center;
  gap: 16px;
  max-width: 1200px;
  margin: 0 auto;
}

.back-button {
  width: 40px;
  height: 40px;
  border: none;
  background: #f0f0f0;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.back-button:hover {
  background: #e0e0e0;
}

.back-button .emoji-icon {
  font-size: 20px;
  color: #666;
}

.page-info h1 {
  margin: 0;
  font-size: 20px;
  color: #333;
}

.page-info p {
  margin: 4px 0 0 0;
  font-size: 14px;
  color: #666;
}

.filter-section {
  background: white;
  border-bottom: 1px solid #e0e0e0;
  padding: 16px 20px;
}

.filter-container {
  max-width: 1200px;
  margin: 0 auto;
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 20px;
}

.category-filters {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.category-btn {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 16px;
  border: 1px solid #e0e0e0;
  background: white;
  color: #666;
  border-radius: 20px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 14px;
}

.category-btn.active {
  background: #d32f2f;
  color: white;
  border-color: #d32f2f;
}

.category-btn:hover {
  border-color: #d32f2f;
}

.category-btn .emoji-icon {
  font-size: 14px;
}

.sort-select {
  padding: 8px 12px;
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  background: white;
  color: #666;
  font-size: 14px;
  cursor: pointer;
}

.popular-content {
  padding: 20px;
}

.popular-container-inner {
  max-width: 1200px;
  margin: 0 auto;
}

.palettes-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: 20px;
  margin-bottom: 40px;
}

.palette-card {
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  cursor: pointer;
  transition: all 0.3s ease;
}

.palette-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.palette-colors {
  display: flex;
  height: 100px;
}

.palette-color {
  flex: 1;
  transition: all 0.3s ease;
}

.palette-card:hover .palette-color {
  transform: scaleY(1.05);
}

.palette-info {
  padding: 20px;
}

.palette-title {
  margin: 0 0 8px 0;
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.palette-description {
  margin: 0 0 12px 0;
  font-size: 14px;
  color: #666;
  line-height: 1.4;
}

.palette-tags {
  display: flex;
  gap: 6px;
  margin-bottom: 12px;
  flex-wrap: wrap;
}

.palette-tag {
  padding: 4px 8px;
  background: #f0f0f0;
  color: #666;
  border-radius: 12px;
  font-size: 12px;
}

.palette-stats {
  display: flex;
  gap: 16px;
  margin-bottom: 16px;
  font-size: 14px;
  color: #666;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 4px;
}

.stat-item .emoji-icon {
  font-size: 14px;
}

.palette-actions {
  display: flex;
  gap: 8px;
}

.action-btn {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 6px 12px;
  border: 1px solid #e0e0e0;
  background: white;
  color: #666;
  border-radius: 6px;
  cursor: pointer;
  font-size: 12px;
  transition: all 0.3s ease;
  flex: 1;
  justify-content: center;
}

.action-btn:hover {
  border-color: #d32f2f;
  color: #d32f2f;
}

.like-btn.active {
  background: #d32f2f;
  color: white;
  border-color: #d32f2f;
}

.action-btn .emoji-icon {
  font-size: 12px;
}

.load-more {
  display: flex;
  justify-content: center;
}

.load-more-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 24px;
  border: none;
  background: white;
  color: #666;
  border-radius: 8px;
  cursor: pointer;
  font-size: 14px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  transition: all 0.3s ease;
}

.load-more-btn:hover:not(:disabled) {
  background: #f5f5f5;
  transform: translateY(-2px);
}

.load-more-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.load-more-btn .emoji-icon {
  font-size: 16px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .popular-content {
    padding: 16px;
  }
  
  .palettes-grid {
    grid-template-columns: 1fr;
  }
  
  .filter-container {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }
  
  .category-filters {
    justify-content: center;
  }
  
  .category-btn {
    padding: 6px 12px;
    font-size: 12px;
  }
}
</style>

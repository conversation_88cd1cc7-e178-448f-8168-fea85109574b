<template>
  <div class="search-results-container">
    <!-- 顶部导航 -->
    <header class="header">
      <div class="nav-container">
        <button class="back-button" @click="goBack">
          <span class="emoji-icon">←</span>
        </button>
        <div class="search-info">
          <h1>搜索结果</h1>
          <p>{{ searchType }} - {{ searchQuery }}</p>
        </div>
      </div>
    </header>

    <!-- 搜索结果 -->
    <main class="results-content">
      <div class="results-container">
        <div v-if="loading" class="loading">
          <div class="spinner"></div>
          <p>正在搜索...</p>
        </div>
        
        <div v-else-if="results.length === 0" class="no-results">
          <span class="emoji-icon" style="font-size: 64px; opacity: 0.5;">🔍</span>
          <h3>未找到相关结果</h3>
          <p>请尝试其他搜索词或检查拼写</p>
        </div>

        <div v-else class="results-list">
          <div 
            v-for="result in results" 
            :key="result.id"
            class="result-item"
            @click="selectResult(result)"
          >
            <div class="result-color" :style="{ backgroundColor: result.color }"></div>
            <div class="result-info">
              <h3>{{ result.name }}</h3>
              <p class="result-code">{{ result.code }}</p>
              <p class="result-description">{{ result.description }}</p>
            </div>
          </div>
        </div>
      </div>
    </main>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'

const route = useRoute()
const router = useRouter()

interface ColorResult {
  id: number
  name: string
  code: string
  color: string
  description: string
}

const loading = ref(true)
const results = ref<ColorResult[]>([])

const searchType = computed(() => {
  const types: Record<string, string> = {
    color: '色号查询',
    rgb: 'RGB查询',
    cmyk: 'CMYK查询',
    hex: 'HEX查询',
    traditional: '传图识色'
  }
  return types[route.query.type as string] || '搜索'
})

const searchQuery = computed(() => route.query.q as string || '')

// 模拟搜索结果
const mockResults = [
  {
    id: 1,
    name: '中国红',
    code: 'C0M100Y100K0',
    color: '#DC143C',
    description: '传统中国红色，象征喜庆和吉祥'
  },
  {
    id: 2,
    name: '天空蓝',
    code: 'C100M0Y0K0',
    color: '#87CEEB',
    description: '清澈的天空蓝色，给人宁静的感觉'
  },
  {
    id: 3,
    name: '翡翠绿',
    code: 'C80M0Y100K0',
    color: '#50C878',
    description: '如翡翠般的绿色，充满生机'
  }
]

onMounted(async () => {
  // 模拟API调用
  setTimeout(() => {
    if (searchQuery.value) {
      results.value = mockResults.filter(item => 
        item.name.includes(searchQuery.value) || 
        item.code.includes(searchQuery.value) ||
        item.description.includes(searchQuery.value)
      )
    } else {
      results.value = mockResults
    }
    loading.value = false
  }, 1000)
})

const goBack = () => {
  router.back()
}

const selectResult = (result: any) => {
  console.log('选择了颜色:', result)
  // 跳转到颜色详情页面
  router.push(`/color/${result.color.replace('#', '')}`)
}
</script>

<style scoped>
.search-results-container {
  min-height: 100vh;
  background: #f5f5f5;
}

.header {
  background: white;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  padding: 16px 20px;
}

.nav-container {
  display: flex;
  align-items: center;
  gap: 16px;
  max-width: 800px;
  margin: 0 auto;
}

.back-button {
  width: 40px;
  height: 40px;
  border: none;
  background: #f0f0f0;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.back-button:hover {
  background: #e0e0e0;
}

.back-button .emoji-icon {
  font-size: 20px;
  color: #666;
}

.search-info h1 {
  margin: 0;
  font-size: 20px;
  color: #333;
}

.search-info p {
  margin: 4px 0 0 0;
  font-size: 14px;
  color: #666;
}

.results-content {
  padding: 20px;
}

.results-container {
  max-width: 800px;
  margin: 0 auto;
}

.loading {
  text-align: center;
  padding: 60px 20px;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #d32f2f;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.no-results {
  text-align: center;
  padding: 60px 20px;
  color: #666;
}

.no-results .emoji-icon {
  display: block;
  margin-bottom: 16px;
}

.results-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.result-item {
  background: white;
  border-radius: 12px;
  padding: 20px;
  display: flex;
  align-items: center;
  gap: 16px;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.result-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0,0,0,0.15);
}

.result-color {
  width: 60px;
  height: 60px;
  border-radius: 8px;
  border: 2px solid #e0e0e0;
  flex-shrink: 0;
}

.result-info {
  flex: 1;
}

.result-info h3 {
  margin: 0 0 4px 0;
  font-size: 18px;
  color: #333;
}

.result-code {
  margin: 0 0 8px 0;
  font-size: 14px;
  color: #d32f2f;
  font-weight: 500;
}

.result-description {
  margin: 0;
  font-size: 14px;
  color: #666;
  line-height: 1.4;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .header {
    padding: 12px 16px;
  }
  
  .results-content {
    padding: 16px;
  }
  
  .result-item {
    padding: 16px;
  }
  
  .result-color {
    width: 50px;
    height: 50px;
  }
}
</style>

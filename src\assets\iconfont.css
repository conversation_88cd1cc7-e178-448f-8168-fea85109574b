/* iconfont SVG symbol 样式 */
.icon {
  width: 1em;
  height: 1em;
  vertical-align: -0.15em;
  fill: currentColor;
  overflow: hidden;
}

/* emoji图标样式 */
.emoji-icon {
  font-family: "Apple Color Emoji", "Segoe UI Emoji", "Noto Color Emoji", sans-serif;
  font-style: normal;
  font-weight: normal;
  line-height: 1;
  display: inline-block;
  text-align: center;
}

/* 图标映射 - 如果SVG不可用则显示emoji */
.icon-palette-emoji::before { content: "🎨"; }
.icon-search-emoji::before { content: "🔍"; }
.icon-box-emoji::before { content: "📦"; }
.icon-tag-emoji::before { content: "🏷️"; }
.icon-heart-emoji::before { content: "❤️"; }
.icon-book-emoji::before { content: "📚"; }
.icon-arrow-left-emoji::before { content: "←"; }

/* 确保图标在不同尺寸下都能正常显示 */
.feature-icon .icon {
  width: 32px;
  height: 32px;
}

.logo-icon .icon {
  width: 48px;
  height: 48px;
}

.search-button .icon {
  width: 20px;
  height: 20px;
}

.back-button .icon {
  width: 20px;
  height: 20px;
}

/* emoji备用方案样式 */
.emoji-fallback {
  display: none;
  font-family: "Apple Color Emoji", "Segoe UI Emoji", "Noto Color Emoji", sans-serif;
  font-style: normal;
  font-weight: normal;
  line-height: 1;
}

.feature-icon .emoji-fallback {
  font-size: 32px;
}

.logo-icon .emoji-fallback {
  font-size: 48px;
}

.search-button .emoji-fallback {
  font-size: 20px;
}

.back-button .emoji-fallback {
  font-size: 20px;
}

/* 当SVG图标不可用时显示emoji */
.icon:not([href]):not([xlink\\:href]) + .emoji-fallback,
.icon[href=""] + .emoji-fallback,
.icon[xlink\\:href=""] + .emoji-fallback {
  display: inline-block;
}

.icon:not([href]):not([xlink\\:href]),
.icon[href=""],
.icon[xlink\\:href=""] {
  display: none;
}

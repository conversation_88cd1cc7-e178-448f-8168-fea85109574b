<template>
  <div class="image-color-container">
    <!-- 顶部导航 -->
    <header class="header">
      <div class="nav-container">
        <button class="back-button" @click="goBack">
          <span class="emoji-icon">←</span>
        </button>
        <div class="page-info">
          <h1>传图识色</h1>
          <p>上传图片，提取主要颜色</p>
        </div>
      </div>
    </header>

    <!-- 主要内容 -->
    <main class="image-color-content">
      <div class="image-color-container-inner">
        <!-- 上传区域 -->
        <div class="upload-section">
          <div
            class="upload-area"
            :class="{ 'drag-over': isDragOver }"
            @drop="handleDrop"
            @dragover.prevent="isDragOver = true"
            @dragleave="isDragOver = false"
            @click="triggerFileInput"
          >
            <input
              ref="fileInput"
              type="file"
              accept="image/*"
              @change="handleFileSelect"
              style="display: none"
            />

            <div v-if="!selectedImage" class="upload-placeholder">
              <span class="upload-icon">📷</span>
              <h3>选择或拖拽图片</h3>
              <p>支持 JPG、PNG、GIF 格式</p>
              <button class="upload-button">
                <span class="emoji-icon">📁</span>
                选择图片
              </button>
            </div>

            <div v-else class="image-preview">
              <img :src="selectedImage" alt="预览图片" />
              <div class="image-overlay">
                <button class="change-image-button" @click.stop="triggerFileInput">
                  <span class="emoji-icon">🔄</span>
                  更换图片
                </button>
              </div>
            </div>
          </div>
        </div>

        <!-- 图库区域 -->
        <div class="gallery-section">
          <h3>
            <span class="emoji-icon">🖼️</span>
            示例图库
          </h3>
          <p class="gallery-description">点击下方图片快速体验传图识色功能</p>

          <div class="gallery-grid">
            <div
              v-for="(galleryImage, index) in galleryImages"
              :key="index"
              class="gallery-item"
              @click="selectGalleryImage(galleryImage)"
            >
              <img :src="galleryImage.url" :alt="galleryImage.title" />
              <div class="gallery-item-overlay">
                <div class="gallery-item-info">
                  <h4>{{ galleryImage.title }}</h4>
                  <p>{{ galleryImage.description }}</p>
                </div>
                <button class="gallery-select-button">
                  <span class="emoji-icon">🎨</span>
                  分析颜色
                </button>
              </div>
            </div>
          </div>
        </div>

        <!-- 分析结果 -->
        <div v-if="extractedColors.length > 0" class="results-section">
          <h3>提取的颜色</h3>
          
          <!-- 主要颜色 -->
          <div class="main-colors">
            <div 
              v-for="(color, index) in extractedColors" 
              :key="index"
              class="color-item"
              @click="viewColorDetail(color)"
            >
              <div class="color-preview" :style="{ backgroundColor: color.hex }"></div>
              <div class="color-info">
                <div class="color-hex">{{ color.hex }}</div>
                <div class="color-percentage">{{ color.percentage }}%</div>
                <div class="color-rgb">RGB({{ color.rgb.r }}, {{ color.rgb.g }}, {{ color.rgb.b }})</div>
              </div>
              <button class="copy-color-button" @click.stop="copyColor(color.hex)">
                <span class="emoji-icon">📋</span>
              </button>
            </div>
          </div>

          <!-- 色彩统计 -->
          <div class="color-stats">
            <h4>色彩分析</h4>
            <div class="stats-grid">
              <div class="stat-item">
                <span class="stat-label">主色调</span>
                <span class="stat-value">{{ dominantColor?.name || '未知' }}</span>
              </div>
              <div class="stat-item">
                <span class="stat-label">色彩数量</span>
                <span class="stat-value">{{ extractedColors.length }} 种</span>
              </div>
              <div class="stat-item">
                <span class="stat-label">色彩丰富度</span>
                <span class="stat-value">{{ colorRichness }}</span>
              </div>
            </div>
          </div>

          <!-- 操作按钮 -->
          <div class="action-buttons">
            <button class="action-button primary" @click="savePalette">
              <span class="emoji-icon">💾</span>
              保存色板
            </button>
            <button class="action-button secondary" @click="exportColors">
              <span class="emoji-icon">📤</span>
              导出颜色
            </button>
            <button class="action-button secondary" @click="findSimilar">
              <span class="emoji-icon">🔍</span>
              查找相似
            </button>
          </div>
        </div>

        <!-- 加载状态 -->
        <div v-if="isAnalyzing" class="analyzing-section">
          <div class="analyzing-animation">
            <span class="emoji-icon">🎨</span>
          </div>
          <h3>正在分析图片...</h3>
          <p>请稍候，我们正在提取图片中的主要颜色</p>
        </div>
      </div>
    </main>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useRouter } from 'vue-router'

interface ExtractedColor {
  hex: string
  rgb: { r: number; g: number; b: number }
  percentage: number
  name?: string
}

const router = useRouter()

const fileInput = ref<HTMLInputElement>()
const selectedImage = ref<string>('')
const isDragOver = ref(false)
const isAnalyzing = ref(false)
const extractedColors = ref<ExtractedColor[]>([])

// 图库数据
const galleryImages = ref([
  {
    url: 'https://cdn.qtccolor.com/imgld/idea/639902/22544_color_300.jpg',
    title: '现代简约风格',
    description: '简洁明快的现代设计色彩'
  },
  {
    url: 'https://cdn.qtccolor.com/imgld/idea/639902/22544_color_300.jpg',
    title: '自然田园风格',
    description: '清新自然的田园色彩搭配'
  },
  {
    url: 'https://cdn.qtccolor.com/imgld/idea/639902/22544_color_300.jpg',
    title: '复古怀旧风格',
    description: '经典复古的色彩组合'
  },
  {
    url: 'https://cdn.qtccolor.com/imgld/idea/639902/22544_color_300.jpg',
    title: '浪漫温馨风格',
    description: '温暖浪漫的色彩氛围'
  },
  {
    url: 'https://cdn.qtccolor.com/imgld/idea/639902/22544_color_300.jpg',
    title: '商务专业风格',
    description: '稳重专业的商务色彩'
  },
  {
    url: 'https://cdn.qtccolor.com/imgld/idea/639902/22544_color_300.jpg',
    title: '创意艺术风格',
    description: '充满创意的艺术色彩'
  }
])

const dominantColor = computed(() => {
  return extractedColors.value.length > 0 ? extractedColors.value[0] : null
})

const colorRichness = computed(() => {
  const count = extractedColors.value.length
  if (count <= 3) return '单调'
  if (count <= 6) return '适中'
  if (count <= 10) return '丰富'
  return '非常丰富'
})

const triggerFileInput = () => {
  fileInput.value?.click()
}

const handleFileSelect = (event: Event) => {
  const target = event.target as HTMLInputElement
  const file = target.files?.[0]
  if (file) {
    processImage(file)
  }
}

const handleDrop = (event: DragEvent) => {
  event.preventDefault()
  isDragOver.value = false
  
  const files = event.dataTransfer?.files
  if (files && files.length > 0) {
    processImage(files[0])
  }
}

const processImage = (file: File) => {
  if (!file.type.startsWith('image/')) {
    alert('请选择图片文件')
    return
  }

  const reader = new FileReader()
  reader.onload = (e) => {
    selectedImage.value = e.target?.result as string
    analyzeImage(selectedImage.value)
  }
  reader.readAsDataURL(file)
}

const selectGalleryImage = (galleryImage: any) => {
  selectedImage.value = galleryImage.url
  analyzeImage(galleryImage.url)
}

const analyzeImage = async (imageDataUrl: string) => {
  isAnalyzing.value = true
  extractedColors.value = []

  try {
    // 创建canvas来分析图片
    const img = new Image()
    img.onload = () => {
      const canvas = document.createElement('canvas')
      const ctx = canvas.getContext('2d')
      
      if (!ctx) return

      // 缩放图片以提高性能
      const maxSize = 200
      const scale = Math.min(maxSize / img.width, maxSize / img.height)
      canvas.width = img.width * scale
      canvas.height = img.height * scale
      
      ctx.drawImage(img, 0, 0, canvas.width, canvas.height)
      
      // 提取颜色
      const colors = extractColorsFromCanvas(canvas, ctx)
      extractedColors.value = colors
      isAnalyzing.value = false
    }
    img.src = imageDataUrl
  } catch (error) {
    console.error('图片分析失败:', error)
    isAnalyzing.value = false
  }
}

const extractColorsFromCanvas = (canvas: HTMLCanvasElement, ctx: CanvasRenderingContext2D): ExtractedColor[] => {
  const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height)
  const data = imageData.data
  const colorMap = new Map<string, number>()
  
  // 采样像素（每隔几个像素取一个，提高性能）
  const step = 4
  for (let i = 0; i < data.length; i += step * 4) {
    const r = data[i]
    const g = data[i + 1]
    const b = data[i + 2]
    const a = data[i + 3]
    
    // 跳过透明像素
    if (a < 128) continue
    
    // 量化颜色（减少颜色数量）
    const quantizedR = Math.round(r / 32) * 32
    const quantizedG = Math.round(g / 32) * 32
    const quantizedB = Math.round(b / 32) * 32
    
    const hex = rgbToHex(quantizedR, quantizedG, quantizedB)
    colorMap.set(hex, (colorMap.get(hex) || 0) + 1)
  }
  
  // 排序并取前几个颜色
  const sortedColors = Array.from(colorMap.entries())
    .sort((a, b) => b[1] - a[1])
    .slice(0, 8)
  
  const totalPixels = sortedColors.reduce((sum, [, count]) => sum + count, 0)
  
  return sortedColors.map(([hex, count]) => {
    const rgb = hexToRgb(hex)
    return {
      hex,
      rgb,
      percentage: Math.round((count / totalPixels) * 100)
    }
  })
}

const rgbToHex = (r: number, g: number, b: number): string => {
  return "#" + ((1 << 24) + (r << 16) + (g << 8) + b).toString(16).slice(1)
}

const hexToRgb = (hex: string): { r: number; g: number; b: number } => {
  const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex)
  return result ? {
    r: parseInt(result[1], 16),
    g: parseInt(result[2], 16),
    b: parseInt(result[3], 16)
  } : { r: 0, g: 0, b: 0 }
}

const viewColorDetail = (color: ExtractedColor) => {
  router.push(`/color/${color.hex.replace('#', '')}`)
}

const copyColor = async (hex: string) => {
  try {
    await navigator.clipboard.writeText(hex)
    console.log('已复制颜色:', hex)
  } catch (err) {
    console.error('复制失败:', err)
  }
}

const savePalette = () => {
  console.log('保存色板')
  // 实现保存色板功能
}

const exportColors = () => {
  console.log('导出颜色')
  // 实现导出功能
}

const findSimilar = () => {
  console.log('查找相似颜色')
  // 实现查找相似颜色功能
}

const goBack = () => {
  router.back()
}
</script>

<style scoped>
.image-color-container {
  min-height: 100vh;
  background: #f5f5f5;
}

.header {
  background: white;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  padding: 16px 20px;
  position: sticky;
  top: 0;
  z-index: 100;
}

.nav-container {
  display: flex;
  align-items: center;
  gap: 16px;
  max-width: 800px;
  margin: 0 auto;
}

.back-button {
  width: 40px;
  height: 40px;
  border: none;
  background: #f0f0f0;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.back-button:hover {
  background: #e0e0e0;
}

.back-button .emoji-icon {
  font-size: 20px;
  color: #666;
}

.page-info h1 {
  margin: 0;
  font-size: 20px;
  color: #333;
}

.page-info p {
  margin: 4px 0 0 0;
  font-size: 14px;
  color: #666;
}

.image-color-content {
  padding: 20px;
}

.image-color-container-inner {
  max-width: 800px;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.upload-section {
  background: white;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.gallery-section {
  background: white;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.gallery-section h3 {
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 0 0 8px 0;
  font-size: 18px;
  color: #333;
}

.gallery-section h3 .emoji-icon {
  font-size: 20px;
}

.gallery-description {
  margin: 0 0 20px 0;
  color: #666;
  font-size: 14px;
}

.gallery-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 16px;
}

.gallery-item {
  position: relative;
  border-radius: 12px;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 2px solid #e0e0e0;
}

.gallery-item:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(0,0,0,0.15);
  border-color: #d32f2f;
}

.gallery-item img {
  width: 100%;
  height: 150px;
  object-fit: cover;
  display: block;
}

.gallery-item-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(transparent, rgba(0,0,0,0.8));
  color: white;
  padding: 16px;
  transform: translateY(100%);
  transition: transform 0.3s ease;
}

.gallery-item:hover .gallery-item-overlay {
  transform: translateY(0);
}

.gallery-item-info h4 {
  margin: 0 0 4px 0;
  font-size: 14px;
  font-weight: 600;
}

.gallery-item-info p {
  margin: 0 0 12px 0;
  font-size: 12px;
  opacity: 0.9;
  line-height: 1.3;
}

.gallery-select-button {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 12px;
  border: none;
  background: #d32f2f;
  color: white;
  border-radius: 6px;
  cursor: pointer;
  font-size: 12px;
  font-weight: 500;
  transition: background-color 0.3s ease;
  width: 100%;
  justify-content: center;
}

.gallery-select-button:hover {
  background: #b71c1c;
}

.gallery-select-button .emoji-icon {
  font-size: 14px;
}

.upload-area {
  border: 2px dashed #ddd;
  border-radius: 12px;
  padding: 40px 20px;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  min-height: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.upload-area.drag-over {
  border-color: #d32f2f;
  background: #fafafa;
}

.upload-area:hover {
  border-color: #d32f2f;
}

.upload-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
}

.upload-icon {
  font-size: 48px;
}

.upload-placeholder h3 {
  margin: 0;
  font-size: 18px;
  color: #333;
}

.upload-placeholder p {
  margin: 0;
  color: #666;
  font-size: 14px;
}

.upload-button {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 24px;
  border: none;
  background: #d32f2f;
  color: white;
  border-radius: 8px;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.3s ease;
}

.upload-button:hover {
  background: #b71c1c;
}

.image-preview {
  position: relative;
  width: 100%;
  height: 100%;
}

.image-preview img {
  max-width: 100%;
  max-height: 300px;
  border-radius: 8px;
  object-fit: contain;
}

.image-overlay {
  position: absolute;
  top: 8px;
  right: 8px;
}

.change-image-button {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 12px;
  border: none;
  background: rgba(0,0,0,0.7);
  color: white;
  border-radius: 6px;
  cursor: pointer;
  font-size: 12px;
  transition: background-color 0.3s ease;
}

.change-image-button:hover {
  background: rgba(0,0,0,0.8);
}

.results-section {
  background: white;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.results-section h3 {
  margin: 0 0 20px 0;
  font-size: 18px;
  color: #333;
}

.main-colors {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-bottom: 24px;
}

.color-item {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 12px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.color-item:hover {
  border-color: #d32f2f;
  transform: translateY(-2px);
}

.color-preview {
  width: 50px;
  height: 50px;
  border-radius: 8px;
  border: 2px solid #e0e0e0;
  flex-shrink: 0;
}

.color-info {
  flex: 1;
}

.color-hex {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  font-family: 'Courier New', monospace;
}

.color-percentage {
  font-size: 14px;
  color: #d32f2f;
  font-weight: 500;
}

.color-rgb {
  font-size: 12px;
  color: #666;
  font-family: 'Courier New', monospace;
}

.copy-color-button {
  width: 36px;
  height: 36px;
  border: 1px solid #ddd;
  background: #f9f9f9;
  border-radius: 6px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.copy-color-button:hover {
  background: #f0f0f0;
  border-color: #ccc;
}

.color-stats {
  margin-bottom: 24px;
}

.color-stats h4 {
  margin: 0 0 12px 0;
  font-size: 16px;
  color: #333;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 12px;
}

.stat-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
  padding: 12px;
  background: #f9f9f9;
  border-radius: 8px;
}

.stat-label {
  font-size: 12px;
  color: #666;
}

.stat-value {
  font-size: 14px;
  font-weight: 600;
  color: #333;
}

.action-buttons {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

.action-button {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 20px;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.3s ease;
  flex: 1;
  min-width: 120px;
  justify-content: center;
}

.action-button.primary {
  background: #d32f2f;
  color: white;
}

.action-button.primary:hover {
  background: #b71c1c;
}

.action-button.secondary {
  background: #f5f5f5;
  color: #666;
  border: 1px solid #ddd;
}

.action-button.secondary:hover {
  background: #e0e0e0;
}

.analyzing-section {
  background: white;
  border-radius: 12px;
  padding: 40px 20px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  text-align: center;
}

.analyzing-animation {
  font-size: 48px;
  animation: pulse 2s infinite;
  margin-bottom: 16px;
}

@keyframes pulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.1); }
}

.analyzing-section h3 {
  margin: 0 0 8px 0;
  font-size: 18px;
  color: #333;
}

.analyzing-section p {
  margin: 0;
  color: #666;
  font-size: 14px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .image-color-content {
    padding: 16px;
  }

  .action-buttons {
    flex-direction: column;
  }

  .action-button {
    flex: none;
  }

  .main-colors {
    gap: 8px;
  }

  .color-item {
    padding: 8px;
  }

  .gallery-grid {
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    gap: 12px;
  }

  .gallery-item img {
    height: 120px;
  }

  .gallery-item-overlay {
    padding: 12px;
  }

  .gallery-item-info h4 {
    font-size: 13px;
  }

  .gallery-item-info p {
    font-size: 11px;
  }
}
</style>
